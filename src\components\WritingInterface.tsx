import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Book, Chapter, Scene, ProjectType } from '../types';
import {
  ChevronDown,
  ChevronRight,
  Plus,
  ArrowRight,
  Focus,
  Edit3,
  Layers3,
  Eye,
  Trash2,
  Edit,
  FileText,
  LogOut,
  Clock,
  CheckCircle,
  AlertCircle,
  Moon,
  Sun,
  BarChart3,
  Settings,
  Share2,
  MessageSquare
} from 'lucide-react';
import { 
  createNewChapter, 
  createNewScene, 
  updateBookWordCount, 
  countWords,
  deleteChapter,
  deleteScene
} from '../utils/bookUtils';
import { RichTextEditor } from './RichTextEditor';
import { WritingToolsModal } from './WritingToolsModal';
import { WritingTimer } from './WritingTimer';
import { SettingsModal } from './SettingsModal';
import { useSceneAutosave } from '../hooks/useSceneAutosave';
import { useAnalytics } from '../hooks/useAnalytics';
import { useImageBrightness } from '../hooks/useImageBrightness';

import { ProjectTypeSelectionModal } from './ProjectTypeSelectionModal';
import { ProjectDetailsModal } from './ProjectDetailsModal';
import { ShareDocumentModal } from './ShareDocumentModal';
import { SharedDocumentsModal } from './SharedDocumentsModal';
import { ReturnedDocumentNotification } from './ReturnedDocumentNotification';
import { ReviewEditsModal } from './ReviewEditsModal';
import { useDocumentSharing } from '../hooks/useDocumentSharing';

// Add CSS for focus mode styling
const focusModeStyles = `
.focus-mode-timer [class*="bg-white/"] {
  background-color: rgb(255 255 255) !important;
  border-color: rgb(209 213 219) !important;
}
.focus-mode-timer [class*="text-white"] {
  color: rgb(0 0 0) !important;
}
.focus-mode-timer [class*="bg-black/"] {
  background-color: rgb(255 255 255) !important;
  border-color: rgb(209 213 219) !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1) !important;
}
.focus-mode [class*="bg-white"][class*="rounded-2xl"][class*="shadow-2xl"] {
  box-shadow: none !important;
  border-radius: 0 !important;
  border: none !important;
}
.dark-mode {
  background-color: rgb(25, 25, 25) !important;
  color: rgb(243 244 246) !important;
}
.dark-mode .prose {
  color: rgb(243 244 246) !important;
}
.dark-mode [class*="border-gray-200"] {
  border-color: rgb(75 85 99) !important;
}
.dark-mode [class*="bg-gray-100"] {
  background-color: rgb(55 65 81) !important;
}
.dark-mode [class*="text-gray-600"] {
  color: rgb(209 213 219) !important;
}
.focus-mode {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.focus-mode::-webkit-scrollbar {
  display: none;
}
.focus-mode-container {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.focus-mode-container::-webkit-scrollbar {
  display: none;
}
body.focus-mode-active {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
body.focus-mode-active::-webkit-scrollbar {
  display: none;
}
html.focus-mode-active {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
html.focus-mode-active::-webkit-scrollbar {
  display: none;
}
.read-only-editor .ProseMirror {
  cursor: default;
  user-select: text;
}
.read-only-editor .ProseMirror:focus {
  outline: none;
}
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #1db954;
  cursor: pointer;
}
.slider::-moz-range-thumb {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #1db954;
  cursor: pointer;
  border: none;
}
`;

interface User {
  email: string | null;
}

interface WritingInterfaceProps {
  book: Book;
  books: Book[];
  onUpdateBook: (book: Book) => void;
  onCreateBook: (title: string, author: string, projectType: ProjectType) => void;
  onSelectBook: (book: Book) => void;
  onDeleteBook: (bookId: string) => void;
  onSignOut: () => void;
  onNavigateToProjectElements: () => void;
  onNavigateToAnalytics: () => void;
  user: User;
  backgroundImage: string;
  onBackgroundImageChange: (url: string) => void;
}

export const WritingInterface: React.FC<WritingInterfaceProps> = ({ 
  book, 
  books,
  onUpdateBook,
  onCreateBook,
  onSelectBook,
  onDeleteBook,
  onSignOut,
  onNavigateToProjectElements,
  onNavigateToAnalytics,
  user,
  backgroundImage,
  onBackgroundImageChange
}) => {
  // Add focus mode styles to document head
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = focusModeStyles;
    document.head.appendChild(styleElement);
    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  const [selectedChapter, setSelectedChapter] = useState<Chapter | null>(null);
  const [selectedScene, setSelectedScene] = useState<Scene | null>(null);
  const [expandedChapters, setExpandedChapters] = useState<Set<string>>(new Set());
  const [showNewChapterInput, setShowNewChapterInput] = useState(false);
  const [showNewSceneInput, setShowNewSceneInput] = useState<string | null>(null);
  const [showProjectTypeModal, setShowProjectTypeModal] = useState(false);
  const [showProjectDetailsModal, setShowProjectDetailsModal] = useState(false);
  const [selectedProjectType, setSelectedProjectType] = useState<ProjectType | null>(null);
  const [newChapterTitle, setNewChapterTitle] = useState('');
  const [newSceneTitle, setNewSceneTitle] = useState('');
  const [focusMode, setFocusMode] = useState(false);
  const [editingChapter, setEditingChapter] = useState<string | null>(null);
  const [editingScene, setEditingScene] = useState<string | null>(null);
  const [editingBook, setEditingBook] = useState<string | null>(null);
  const [editChapterTitle, setEditChapterTitle] = useState('');
  const [editSceneTitle, setEditSceneTitle] = useState('');
  const [editBookTitle, setEditBookTitle] = useState('');
  const [editBookAuthor, setEditBookAuthor] = useState('');
  const [showWritingTools, setShowWritingTools] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showSharedDocsModal, setShowSharedDocsModal] = useState(false);
  const [darkMode, setDarkMode] = useState(false);
  const [returnedDocuments, setReturnedDocuments] = useState<any[]>([]);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [selectedReturnedDoc, setSelectedReturnedDoc] = useState<any>(null);

  const navigate = useNavigate();
  
  // Document sharing hook
  const {
    shareDocument,
    checkForReturnedDocuments,
    acceptEdits,
    declineEdits,
    getSharedDocument,
    getComments,
    exportSharedDocumentToPDF,
    revokeSharedDocument,
    declineSharedDocument,
    loading: sharingLoading,
    error: sharingError
  } = useDocumentSharing({
    userId: (user as any)?.uid || '',
    userName: user.displayName || user.email || 'User',
    userEmail: user.email || ''
  });
  const [showToolbars, setShowToolbars] = useState(true);
  const [previousWordCount, setPreviousWordCount] = useState(0);
  
  const { trackWritingSession } = useAnalytics(user?.uid ?? (user as any)?.id ?? '');
  const isBackgroundDark = useImageBrightness(backgroundImage);
  const glassClass = 'bg-black/60';
  const glassClassLight = 'bg-black/40';

  // Scene-specific autosave function
  const saveSceneToFirebase = useCallback(async (sceneId: string, content: string) => {
    if (!selectedScene || !selectedChapter || selectedScene.id !== sceneId) return;
    
    const wordCount = countWords(content);
    const wordsAdded = wordCount - previousWordCount;
    
    // Track writing session if words were added
    if (wordsAdded > 0) {
      await trackWritingSession(wordsAdded, book.id);
    }
    
    setPreviousWordCount(wordCount);
    
    const updatedScene = { ...selectedScene, content, wordCount, updatedAt: new Date().toISOString() };
    const updatedChapter = {
      ...selectedChapter,
      scenes: selectedChapter.scenes.map(scene =>
        scene.id === selectedScene.id ? updatedScene : scene
      ),
      updatedAt: new Date().toISOString()
    };

    const updatedBook = {
      ...book,
      chapters: book.chapters.map(chapter =>
        chapter.id === selectedChapter.id ? updatedChapter : chapter
      )
    };

    const finalBook = updateBookWordCount(updatedBook);
    await onUpdateBook(finalBook);
  }, [selectedScene, selectedChapter, book, onUpdateBook, previousWordCount, trackWritingSession]);

  // Scene-specific autosave hook
  const { isSaving, hasUnsavedChanges, getCurrentContent, updateContent: updateSceneContent } = useSceneAutosave(
    selectedScene?.id || null,
    selectedScene?.content || '',
    saveSceneToFirebase,
    2000, // 2 second delay
    true // enabled
  );

  // Reset all state when user changes
  useEffect(() => {
    setSelectedChapter(null);
    setSelectedScene(null);
    setExpandedChapters(new Set());
  }, [user.email]);
  
  // Check for returned documents
  useEffect(() => {
    const checkReturned = async () => {
      const returned = await checkForReturnedDocuments();
      setReturnedDocuments(returned);
    };
    
    checkReturned();
    
    // Check every 5 minutes
    const interval = setInterval(checkReturned, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [checkForReturnedDocuments]);

  // Auto-hide toolbars in focus mode
  useEffect(() => {
    if (!focusMode) return;

    let timeoutId: NodeJS.Timeout;
    
    const resetTimeout = () => {
      setShowToolbars(true);
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => setShowToolbars(false), 3000);
    };

    const handleActivity = () => resetTimeout();

    resetTimeout();
    document.addEventListener('mousemove', handleActivity);

    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener('mousemove', handleActivity);
    };
  }, [focusMode]);

  // Manage document body background for focus mode dark mode
  useEffect(() => {
    if (focusMode && darkMode) {
      document.body.style.backgroundColor = 'rgb(25, 25, 25)';
    } else {
      document.body.style.backgroundColor = '';
    }

    return () => {
      document.body.style.backgroundColor = '';
    };
  }, [focusMode, darkMode]);

  // Manage focus mode scrollbar hiding
  useEffect(() => {
    if (focusMode) {
      document.body.classList.add('focus-mode-active');
      document.documentElement.classList.add('focus-mode-active');
    } else {
      document.body.classList.remove('focus-mode-active');
      document.documentElement.classList.remove('focus-mode-active');
    }

    return () => {
      document.body.classList.remove('focus-mode-active');
      document.documentElement.classList.remove('focus-mode-active');
    };
  }, [focusMode]);

  useEffect(() => {
    if (book.chapters.length > 0 && !selectedChapter) {
      const firstChapter = book.chapters[0];
      setSelectedChapter(firstChapter);
      setExpandedChapters(new Set([firstChapter.id]));

      if (firstChapter.scenes.length > 0) {
        setSelectedScene(firstChapter.scenes[0]);
      }
    }
  }, [book, selectedChapter]);
  
  // Update previous word count when scene changes
  useEffect(() => {
    if (selectedScene) {
      setPreviousWordCount(selectedScene.wordCount);
    }
  }, [selectedScene?.id]);

  const updateContent = (content: string) => {
    // Update scene-specific content through the autosave hook
    updateSceneContent(content);
  };

  const toggleChapterExpansion = (chapterId: string) => {
    const newExpanded = new Set(expandedChapters);
    if (newExpanded.has(chapterId)) {
      newExpanded.delete(chapterId);
    } else {
      newExpanded.add(chapterId);
    }
    setExpandedChapters(newExpanded);
  };

  const handleCreateChapter = () => {
    if (!newChapterTitle.trim()) return;

    const newChapter = createNewChapter(newChapterTitle.trim(), book.chapters.length);
    const updatedBook = {
      ...book,
      chapters: [...book.chapters, newChapter],
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setNewChapterTitle('');
    setShowNewChapterInput(false);
    setSelectedChapter(newChapter);
    setSelectedScene(null);
    setExpandedChapters(new Set([...expandedChapters, newChapter.id]));
  };

  const handleCreateScene = (chapterId: string) => {
    if (!newSceneTitle.trim()) return;

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) return;

    const newScene = createNewScene(newSceneTitle.trim(), chapter.scenes.length);
    const updatedChapter = {
      ...chapter,
      scenes: [...chapter.scenes, newScene],
      updatedAt: new Date().toISOString()
    };

    const updatedBook = {
      ...book,
      chapters: book.chapters.map(c => c.id === chapterId ? updatedChapter : c),
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setNewSceneTitle('');
    setShowNewSceneInput(null);
    setSelectedChapter(updatedChapter);
    setSelectedScene(newScene);
  };

  const handleSelectProjectType = (projectType: ProjectType) => {
    setSelectedProjectType(projectType);
    setShowProjectTypeModal(false);
    setShowProjectDetailsModal(true);
  };

  const handleCreateProject = (title: string, author: string, projectType: ProjectType) => {
    onCreateBook(title, author, projectType);
    setShowProjectDetailsModal(false);
    setSelectedProjectType(null);
  };

  const handleCloseProjectModals = () => {
    setShowProjectTypeModal(false);
    setShowProjectDetailsModal(false);
    setSelectedProjectType(null);
  };

  const handleBackToProjectTypes = () => {
    setShowProjectDetailsModal(false);
    setShowProjectTypeModal(true);
  };

  const handleDeleteChapter = (chapterId: string) => {
    if (window.confirm('Are you sure you want to delete this chapter? This action cannot be undone.')) {
      const updatedBook = deleteChapter(book, chapterId);
      onUpdateBook(updatedBook);
      
      if (selectedChapter?.id === chapterId) {
        setSelectedChapter(null);
        setSelectedScene(null);
      }
    }
  };

  const handleDeleteScene = (chapterId: string, sceneId: string) => {
    if (window.confirm('Are you sure you want to delete this scene? This action cannot be undone.')) {
      const updatedBook = deleteScene(book, chapterId, sceneId);
      onUpdateBook(updatedBook);
      
      if (selectedScene?.id === sceneId) {
        setSelectedScene(null);
      }
    }
  };

  const handleDeleteBook = (bookId: string) => {
    if (window.confirm('Are you sure you want to delete this book? This action cannot be undone.')) {
      onDeleteBook(bookId);
    }
  };

  const handleEditChapter = (chapter: Chapter) => {
    setEditingChapter(chapter.id);
    setEditChapterTitle(chapter.title);
  };

  const handleSaveChapterEdit = () => {
    if (!editingChapter || !editChapterTitle.trim()) return;

    const updatedBook = {
      ...book,
      chapters: book.chapters.map(chapter => 
        chapter.id === editingChapter 
          ? { ...chapter, title: editChapterTitle.trim(), updatedAt: new Date().toISOString() }
          : chapter
      ),
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setEditingChapter(null);
    setEditChapterTitle('');
  };

  const handleEditScene = (scene: Scene) => {
    setEditingScene(scene.id);
    setEditSceneTitle(scene.title);
  };

  const handleSaveSceneEdit = () => {
    if (!editingScene || !editSceneTitle.trim() || !selectedChapter) return;

    const updatedScene = selectedScene?.id === editingScene 
      ? { ...selectedScene, title: editSceneTitle.trim(), updatedAt: new Date().toISOString() }
      : null;

    const updatedChapter = {
      ...selectedChapter,
      scenes: selectedChapter.scenes.map(scene => 
        scene.id === editingScene 
          ? { ...scene, title: editSceneTitle.trim(), updatedAt: new Date().toISOString() }
          : scene
      ),
      updatedAt: new Date().toISOString()
    };

    const updatedBook = {
      ...book,
      chapters: book.chapters.map(chapter => 
        chapter.id === selectedChapter.id ? updatedChapter : chapter
      ),
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setEditingScene(null);
    setEditSceneTitle('');
    setSelectedChapter(updatedChapter);
    if (updatedScene) {
      setSelectedScene(updatedScene);
    }
  };

  const handleEditBook = (bookToEdit: Book) => {
    setEditingBook(bookToEdit.id);
    setEditBookTitle(bookToEdit.title);
    setEditBookAuthor(bookToEdit.author);
  };

  const handleSaveBookEdit = () => {
    if (!editingBook || !editBookTitle.trim() || !editBookAuthor.trim()) return;

    const updatedBook = {
      ...book,
      title: editBookTitle.trim(),
      author: editBookAuthor.trim(),
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
    setEditingBook(null);
    setEditBookTitle('');
    setEditBookAuthor('');
  };
  
  // Handle document sharing
  const handleShareDocument = async (
    recipientEmail: string,
    permission: 'view' | 'edit',
    message: string,
    expirationDays?: number
  ) => {
    try {
      return await shareDocument(book, recipientEmail, permission, message, expirationDays);
    } catch (err) {
      console.error('Error sharing document:', err);
      return { success: false, error: err };
    }
  };
  
  // Handle opening shared document
  const handleOpenSharedDocument = (sharedDocumentId: string) => {
    // Navigate to the shared document view using React Router
    navigate(`/shared/${sharedDocumentId}`);
  };
  
  // Handle reviewing returned document
  const handleReviewDocument = async (documentId: string) => {
    try {
      const sharedDoc = await getSharedDocument(documentId);
      if (sharedDoc) {
        setSelectedReturnedDoc({
          id: documentId,
          metadata: sharedDoc.metadata,
          book: sharedDoc.book
        });
        setShowReviewModal(true);
      }
    } catch (err) {
      console.error('Error getting shared document:', err);
    }
  };
  
  // Handle dismissing notification
  const handleDismissNotification = (documentId: string) => {
    setReturnedDocuments(prev => prev.filter(doc => doc.id !== documentId));
  };
  
  // Handle accepting all edits
  const handleAcceptAllEdits = async () => {
    if (!selectedReturnedDoc) return { success: false };
    
    try {
      const result = await acceptEdits(selectedReturnedDoc.id, selectedReturnedDoc.book.originalBookId);
      
      if (result.success) {
        // Remove from returned documents list
        setReturnedDocuments(prev => prev.filter(doc => doc.id !== selectedReturnedDoc.id));
        
        // Refresh the current book if it was the one that was edited
        if (book.id === selectedReturnedDoc.book.originalBookId) {
          // This would typically trigger a refresh of the book data
          // For now, we'll just close the modal and let the user refresh manually
        }
      }
      
      return result;
    } catch (err) {
      console.error('Error accepting edits:', err);
      return { success: false };
    }
  };
  
  // Handle declining edits
  const handleDeclineEdits = async () => {
    if (!selectedReturnedDoc) return { success: false };
    
    try {
      const result = await declineEdits(selectedReturnedDoc.id);
      
      if (result.success) {
        // Remove from returned documents list
        setReturnedDocuments(prev => prev.filter(doc => doc.id !== selectedReturnedDoc.id));
      }
      
      return result;
    } catch (err) {
      console.error('Error declining edits:', err);
      return { success: false };
    }
  };
  
  // Handle exporting returned document to PDF
  const handleExportReturnedToPDF = () => {
    if (selectedReturnedDoc) {
      exportSharedDocumentToPDF(selectedReturnedDoc.book);
    }
  };
  
  // Handle getting comments for a shared document
  const handleGetComments = async () => {
    if (!selectedReturnedDoc) return [];
    
    try {
      const comments = await getComments(selectedReturnedDoc.id);
      return comments;
    } catch (err) {
      console.error('Error getting comments:', err);
      return [];
    }
  };

  const handleSaveCharacterFromAI = (character: { name: string; description: string }) => {
    const newCharacter = {
      id: Date.now().toString(36) + Math.random().toString(36).substr(2),
      bookId: book.id,
      name: character.name,
      description: character.description,
      role: '',
      notes: '',
      appearance: '',
      personality: '',
      backstory: '',
      goals: '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const updatedBook = {
      ...book,
      characters: [...book.characters, newCharacter],
      updatedAt: new Date().toISOString()
    };

    onUpdateBook(updatedBook);
  };

  const getCurrentLocation = () => {
    if (selectedScene && selectedChapter) {
      const chapterIndex = book.chapters.findIndex(c => c.id === selectedChapter.id);
      return `Chapter ${chapterIndex + 1}, Scene ${selectedChapter.scenes.findIndex(s => s.id === selectedScene.id) + 1}`;
    }
    return 'Select a scene to start writing';
  };



  const getDisplayContent = () => {
    if (selectedScene && selectedChapter) {
      // Use scene-specific content from autosave hook
      return getCurrentContent();
    }
    return '';
  };

  if (focusMode) {
    return (
      <div 
        className={`min-h-screen relative ${darkMode ? 'bg-gray-900' : ''}`}
        style={{
          backgroundImage: darkMode ? 'none' : `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed'
        }}
      >
        <div className={`absolute inset-0 ${darkMode ? 'bg-gray-900' : 'bg-black/20'}`} />
        
        {/* Full page background */}
        <div 
          className={`absolute inset-0 z-10 flex flex-col focus-mode-container ${darkMode ? '' : 'bg-white'}`}
          style={darkMode ? { backgroundColor: 'rgb(25, 25, 25)' } : {}}
        >
            {/* Header */}
            <div className={`p-6 transition-opacity duration-500 ${showToolbars ? 'opacity-100' : 'opacity-0'}`}>
              <div className="flex items-center justify-between">
                <div>
                  <h1 className={`text-3xl font-serif font-semibold ${darkMode ? 'text-gray-100' : 'text-gray-800'}`}>{book.title}</h1>
                  <p className={`text-sm font-sans ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                    {book.projectType.charAt(0).toUpperCase() + book.projectType.slice(1).replace('-', ' ')} by {book.author}
                  </p>
                </div>
                <div className={`flex items-center space-x-4 font-sans ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  <span>{getCurrentLocation()}</span>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                    <Eye className="w-4 h-4" />
                  </div>
                </div>
              </div>
            </div>

            {/* Editor */}
            <div className="flex-1 px-6 pb-6">
              <div className="max-w-4xl mx-auto">
                <RichTextEditor
                  content={getDisplayContent()}
                  onChange={updateContent}
                  placeholder="Start writing your story..."
                  className={`min-h-[600px] focus-mode ${darkMode ? 'dark-mode' : ''}`}
                  sceneId={selectedScene?.id}
                  hideToolbar={!showToolbars}
                />
              </div>
            </div>

            {/* Bottom Actions */}
            <div className={`p-6 transition-opacity duration-500 ${showToolbars ? 'opacity-100' : 'opacity-0'}`}>
              <div className="flex items-center justify-between max-w-4xl mx-auto">
                <div className="flex items-center space-x-4">
                  <button 
                    onClick={() => setFocusMode(false)}
                    className={`flex items-center px-4 py-2 rounded-xl border transition-colors ${
                      darkMode 
                        ? 'bg-gray-800 hover:bg-gray-700 border-gray-600 text-gray-200 hover:text-gray-100'
                        : 'bg-gray-100 hover:bg-gray-200 border-gray-300 text-gray-700 hover:text-gray-900'
                    }`}
                  >
                    <Focus className="w-4 h-4 mr-2" />
                    <span className="font-sans">Exit Focus Mode</span>
                  </button>

                  <button 
                    onClick={() => setDarkMode(!darkMode)}
                    className={`flex items-center px-4 py-2 rounded-xl border transition-colors ${
                      darkMode 
                        ? 'bg-gray-800 hover:bg-gray-700 border-gray-600 text-gray-200 hover:text-gray-100'
                        : 'bg-gray-100 hover:bg-gray-200 border-gray-300 text-gray-700 hover:text-gray-900'
                    }`}
                  >
                    {darkMode ? <Sun className="w-4 h-4 mr-2" /> : <Moon className="w-4 h-4 mr-2" />}
                    <span className="font-sans">{darkMode ? 'Light' : 'Dark'}</span>
                  </button>

                  <WritingTimer className="focus-mode-timer" />
                </div>

                {/* Autosave Status Indicator */}
                <div className={`flex items-center px-3 py-2 rounded-xl border ${
                  darkMode 
                    ? 'bg-gray-800 border-gray-600 text-gray-300'
                    : 'bg-gray-50 border-gray-200 text-gray-600'
                }`}>
                  {isSaving ? (
                    <>
                      <Clock className="w-4 h-4 mr-2 animate-spin" />
                      <span className="font-sans text-sm">Saving...</span>
                    </>
                  ) : hasUnsavedChanges ? (
                    <>
                      <AlertCircle className="w-4 h-4 mr-2 text-yellow-500" />
                      <span className="font-sans text-sm">Unsaved changes</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      <span className="font-sans text-sm">Saved</span>
                    </>
                  )}
                </div>
              </div>
            </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="min-h-screen relative"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="absolute inset-0 bg-black/20" />
      
      <div className="relative z-10 flex min-h-screen">
        {/* Sidebar */}
        <div className="w-80 p-6 flex flex-col">
          <div className={`${glassClass} backdrop-blur-md rounded-2xl border border-white/10 flex-1 p-6 overflow-y-auto`}>
            <h1 className="text-2xl font-serif font-regular text-white mb-6">WriterOne</h1>
            
            {/* Analytics Button */}
            <button
              onClick={onNavigateToAnalytics}
              className="w-full flex items-center p-3 rounded-lg text-white/80 hover:bg-white/10 hover:text-white transition-colors mb-3"
            >
              <BarChart3 className="w-4 h-4 mr-3" />
              <span className="font-sans">Analytics</span>
            </button>
            
            {/* Settings Button */}
            <button
              onClick={() => setShowSettings(true)}
              className="w-full flex items-center p-3 rounded-lg text-white/80 hover:bg-white/10 hover:text-white transition-colors mb-3"
            >
              <Settings className="w-4 h-4 mr-3" />
              <span className="font-sans">Settings</span>
            </button>
            
            {/* Share Button */}
            <button
              onClick={() => setShowShareModal(true)}
              className="w-full flex items-center p-3 rounded-lg text-white/80 hover:bg-white/10 hover:text-white transition-colors mb-3"
            >
              <Share2 className="w-4 h-4 mr-3" />
              <span className="font-sans">Share Document</span>
            </button>
            
            {/* Shared Documents Button */}
            <button
              onClick={() => setShowSharedDocsModal(true)}
              className="w-full flex items-center p-3 rounded-lg text-white/80 hover:bg-white/10 hover:text-white transition-colors mb-6"
            >
              <MessageSquare className="w-4 h-4 mr-3" />
              <span className="font-sans">Shared Documents</span>
            </button>
            
            {/* Current Book */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                {editingBook === book.id ? (
                  <div className="flex-1 space-y-2">
                    <input
                      type="text"
                      value={editBookTitle}
                      onChange={(e) => setEditBookTitle(e.target.value)}
                      className="w-full px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 text-lg font-serif"
                      placeholder="Book title..."
                    />
                    <input
                      type="text"
                      value={editBookAuthor}
                      onChange={(e) => setEditBookAuthor(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSaveBookEdit()}
                      onBlur={handleSaveBookEdit}
                      className="w-full px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 text-sm font-sans"
                      placeholder="Author name..."
                    />
                  </div>
                ) : (
                  <>
                    <div>
                      <h2 className="text-xl font-serif font-semibold text-white">{book.title}</h2>
                      <p className="text-white/60 text-sm font-sans">
                        {book.projectType.charAt(0).toUpperCase() + book.projectType.slice(1).replace('-', ' ')} by {book.author}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditBook(book)}
                        className="text-white/60 hover:text-white"
                        title="Edit Project"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      {books.length > 1 && (
                        <button 
                          onClick={() => handleDeleteBook(book.id)}
                          className="text-white/60 hover:text-red-400"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </>
                )}
              </div>
              <div className="text-white/60 text-sm font-sans">
                {book.wordCount.toLocaleString()} words • {book.chapters.length} chapters
              </div>
            </div>

            {/* Chapters */}
            <div className="space-y-2 mb-6">
              {book.chapters.map((chapter, chapterIndex) => (
                <div key={chapter.id}>
                  <div className="flex items-center justify-between group">
                    <button
                      onClick={() => {
                        toggleChapterExpansion(chapter.id);
                        setSelectedChapter(chapter);
                        // Don't allow writing at chapter level - must select a scene
                      }}
                      className={`flex-1 flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                        selectedChapter?.id === chapter.id
                          ? 'bg-white/10 text-white/90'
                          : 'text-white/80 hover:bg-white/5 hover:text-white'
                      }`}
                    >
                      {editingChapter === chapter.id ? (
                        <input
                          type="text"
                          value={editChapterTitle}
                          onChange={(e) => setEditChapterTitle(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleSaveChapterEdit()}
                          onBlur={handleSaveChapterEdit}
                          className="flex-1 px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 font-sans"
                          placeholder="Chapter title..."
                          autoFocus
                        />
                      ) : (
                        <span className="font-sans">{chapter.title}</span>
                      )}
                      {expandedChapters.has(chapter.id) ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </button>
                    <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <button 
                        onClick={() => handleEditChapter(chapter)}
                        className="text-white/60 hover:text-white p-1"
                      >
                        <Edit className="w-3 h-3" />
                      </button>
                      <button 
                        onClick={() => handleDeleteChapter(chapter.id)}
                        className="text-white/60 hover:text-red-400 p-1"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                  </div>

                  {expandedChapters.has(chapter.id) && (
                    <div className="ml-4 mt-2 space-y-1">
                      {chapter.scenes.map((scene, sceneIndex) => (
                        <div key={scene.id} className="flex items-center justify-between group">
                          <button
                            onClick={() => {
                              setSelectedChapter(chapter);
                              setSelectedScene(scene);
                            }}
                            className={`flex-1 flex items-center justify-between p-2 rounded-lg text-left transition-colors ${
                              selectedScene?.id === scene.id
                                ? 'bg-white/20 text-white'
                                : 'text-white/60 hover:bg-white/10 hover:text-white/80'
                            }`}
                          >
                            {editingScene === scene.id ? (
                              <input
                                type="text"
                                value={editSceneTitle}
                                onChange={(e) => setEditSceneTitle(e.target.value)}
                                onKeyPress={(e) => e.key === 'Enter' && handleSaveSceneEdit()}
                                onBlur={handleSaveSceneEdit}
                                className="flex-1 px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 text-sm font-sans"
                                placeholder="Scene title..."
                                autoFocus
                              />
                            ) : (
                              <span className="font-sans text-sm">{scene.title}</span>
                            )}
                            <ArrowRight className="w-3 h-3" />
                          </button>
                          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button 
                              onClick={() => handleEditScene(scene)}
                              className="text-white/60 hover:text-white p-1"
                            >
                              <Edit className="w-3 h-3" />
                            </button>
                            <button 
                              onClick={() => handleDeleteScene(chapter.id, scene.id)}
                              className="text-white/60 hover:text-red-400 p-1"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      ))}

                      {showNewSceneInput === chapter.id ? (
                        <div className="p-2">
                          <input
                            type="text"
                            value={newSceneTitle}
                            onChange={(e) => setNewSceneTitle(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleCreateScene(chapter.id)}
                            onBlur={() => setShowNewSceneInput(null)}
                            className="w-full px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 text-sm font-sans"
                            placeholder="Scene title..."
                            autoFocus
                          />
                        </div>
                      ) : (
                        <button
                          onClick={() => setShowNewSceneInput(chapter.id)}
                          className="w-full flex items-center p-2 rounded-lg text-white/60 hover:bg-white/10 hover:text-white/80 transition-colors"
                        >
                          <Plus className="w-3 h-3 mr-2" />
                          <span className="font-sans text-sm">New Scene</span>
                        </button>
                      )}
                    </div>
                  )}
                </div>
              ))}

              {showNewChapterInput ? (
                <div className="p-3">
                  <input
                    type="text"
                    value={newChapterTitle}
                    onChange={(e) => setNewChapterTitle(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleCreateChapter()}
                    onBlur={() => setShowNewChapterInput(false)}
                    className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 font-sans"
                    placeholder="Chapter title..."
                    autoFocus
                  />
                </div>
              ) : (
                <button
                  onClick={() => setShowNewChapterInput(true)}
                  className="w-full flex items-center p-3 rounded-lg text-white/60 hover:bg-white/10 hover:text-white transition-colors"
                >
                  <Plus className="w-4 h-4 mr-3" />
                  <span className="font-sans">New Chapter</span>
                </button>
              )}
            </div>

            {/* Other Projects */}
            <div className="border-t border-white/20 pt-4">
              <h3 className="text-white/80 font-sans text-sm mb-3">Other Projects</h3>
              
              {/* User Info & Sign Out */}
              <div className="mb-4 p-3 bg-white/5 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="text-white/80 font-sans text-sm">{user.email}</div>
                  <button
                    onClick={onSignOut}
                    className="text-white/60 hover:text-white transition-colors"
                  >
                    <LogOut className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              {books.filter(b => b.id !== book.id).map((otherBook) => (
                <button 
                  key={otherBook.id}
                  onClick={() => onSelectBook(otherBook)}
                  className="w-full flex items-center justify-between p-3 rounded-lg text-white/60 hover:bg-white/10 hover:text-white transition-colors mb-2"
                >
                  <div className="text-left">
                    <div className="font-sans">{otherBook.title}</div>
                    <div className="text-xs text-white/40">
                      {otherBook.projectType.charAt(0).toUpperCase() + otherBook.projectType.slice(1).replace('-', ' ')} by {otherBook.author}
                    </div>
                  </div>
                  <ArrowRight className="w-4 h-4" />
                </button>
              ))}
              
              <button
                onClick={() => setShowProjectTypeModal(true)}
                className="w-full flex items-center p-3 rounded-lg text-white/60 hover:bg-white/10 hover:text-white transition-colors"
              >
                <Plus className="w-4 h-4 mr-3" />
                <span className="font-sans">New Project</span>
              </button>
            </div>
          </div>
        </div>

        {/* Main Editor */}
        <div className="flex-1 p-6 flex flex-col min-h-0 h-screen">
          {/* Header */}
          <div className={`${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 p-3 mb-4`}>
            <div className="flex items-center justify-between">
              <h1 className="text-lg font-serif font-regular text-white">{book.title}</h1>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 text-white/80 font-sans text-sm">
                  <span>{getCurrentLocation()}</span>
                  <div className="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center">
                    <Eye className="w-3 h-3" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Editor */}
          <div className="flex-1 mb-4 min-h-0">
            {selectedScene ? (
              <RichTextEditor
                content={getDisplayContent()}
                onChange={updateContent}
                placeholder="Start writing your story..."
                className="h-full"
                sceneId={selectedScene.id}
              />
            ) : (
              <div className="bg-white rounded-2xl shadow-2xl h-full flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <FileText className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-xl font-serif mb-2">Select a scene to start writing</h3>
                  <p className="font-sans">Choose a scene from the sidebar or create a new one to begin writing.</p>
                </div>
              </div>
            )}
          </div>

          {/* Bottom Actions */}
          <div className="flex items-center justify-between">
            <button 
              onClick={() => setFocusMode(true)}
              className={`flex items-center px-4 py-2 ${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 text-white/80 hover:text-white transition-colors`}
            >
              <Focus className="w-4 h-4 mr-2" />
              <span className="font-sans">Focus Mode</span>
            </button>

            <button 
              onClick={() => setShowWritingTools(true)}
              className={`flex items-center px-4 py-2 ${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 text-white/80 hover:text-white transition-colors`}
            >
              <Edit3 className="w-4 h-4 mr-2" />
              <span className="font-sans">Writing Tools</span>
            </button>

            <button
              onClick={onNavigateToProjectElements}
              className="flex items-center px-4 py-2 bg-black/40 backdrop-blur-md rounded-xl border border-white/10 text-white hover:bg-black/50 transition-colors"
            >
              <Layers3 className="w-4 h-4 mr-2" />
              <span className="font-sans">Project Elements</span>
            </button>

            {/* Autosave Status Indicator */}
            <div className={`flex items-center px-3 py-2 ${glassClassLight} backdrop-blur-md rounded-xl border border-white/10 text-white/60`}>
              {isSaving ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  <span className="font-sans text-sm">Saving...</span>
                </>
              ) : hasUnsavedChanges ? (
                <>
                  <AlertCircle className="w-4 h-4 mr-2 text-yellow-400" />
                  <span className="font-sans text-sm">Unsaved</span>
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2 text-green-400" />
                  <span className="font-sans text-sm">Saved</span>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Modals */}
        <WritingToolsModal
          isOpen={showWritingTools}
          onClose={() => setShowWritingTools(false)}
          currentContent={getDisplayContent()}
          onSaveCharacter={handleSaveCharacterFromAI}
        />
        
        <SettingsModal
          isOpen={showSettings}
          onClose={() => setShowSettings(false)}
          currentBackgroundImage={backgroundImage}
          onBackgroundImageChange={onBackgroundImageChange}
        />

        {/* Project Creation Modals */}
        <ProjectTypeSelectionModal
          isOpen={showProjectTypeModal}
          onClose={handleCloseProjectModals}
          onSelectType={handleSelectProjectType}
          backgroundImage={backgroundImage}
        />

        <ProjectDetailsModal
          isOpen={showProjectDetailsModal}
          onClose={handleCloseProjectModals}
          onBack={handleBackToProjectTypes}
          onCreateProject={handleCreateProject}
          projectType={selectedProjectType || 'novel'}
          backgroundImage={backgroundImage}
        />

        {/* Document Sharing Modals */}
        <ShareDocumentModal
          isOpen={showShareModal}
          onClose={() => setShowShareModal(false)}
          onShare={handleShareDocument}
          book={book}
          backgroundImage={backgroundImage}
        />
        
        <SharedDocumentsModal
          isOpen={showSharedDocsModal}
          onClose={() => setShowSharedDocsModal(false)}
          userId={(user as any)?.uid || ''}
          userName={user.displayName || user.email || 'User'}
          userEmail={user.email || ''}
          onOpenSharedDocument={handleOpenSharedDocument}
          backgroundImage={backgroundImage}
        />
        
        {/* Review Edits Modal */}
        {selectedReturnedDoc && (
          <ReviewEditsModal
            isOpen={showReviewModal}
            onClose={() => setShowReviewModal(false)}
            sharedDocument={selectedReturnedDoc}
            originalBook={book} // This should ideally be the original book
            editedBook={selectedReturnedDoc.book}
            onAcceptAllEdits={handleAcceptAllEdits}
            onDeclineEdits={handleDeclineEdits}
            onExportToPDF={handleExportReturnedToPDF}
            getComments={handleGetComments}
            backgroundImage={backgroundImage}
          />
        )}
        
        {/* Returned Document Notifications */}
        {returnedDocuments.map((doc) => (
          <ReturnedDocumentNotification
            key={doc.id}
            documentId={doc.id}
            documentTitle={doc.bookTitle || 'Shared Document'}
            editorName={doc.senderName || 'Editor'}
            onReview={handleReviewDocument}
            onDismiss={handleDismissNotification}
          />
        ))}
      </div>
    </div>
  );
};