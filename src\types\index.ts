export type ProjectType = 'novel' | 'novella' | 'short-story' | 'poem' | 'screenplay' | 'essay' | 'journal';

// Collaboration types removed

export interface Book {
  id: string;
  title: string;
  author: string;
  projectType: ProjectType;
  ownerId: string;
  createdAt: string;
  updatedAt: string;
  wordCount: number;
  chapters: Chapter[];
  characters: Character[];
  plotPoints: PlotPoint[];
  worldbuilding: Worldbuilding;
  settings: BookSettings;
}

export interface Chapter {
  id: string;
  bookId: string;
  title: string;
  content: string;
  wordCount: number;
  scenes: Scene[];
  order: number; // Keep as 'order' for compatibility, map to 'orderIndex' in Firebase
  orderIndex: number; // Firebase field name
  createdAt: string;
  updatedAt: string;
}

export interface Scene {
  id: string;
  chapterId: string;
  title: string;
  content: string;
  wordCount: number;
  order: number; // Keep as 'order' for compatibility, map to 'orderIndex' in Firebase
  orderIndex: number; // Firebase field name
  createdAt: string;
  updatedAt: string;
}

export interface Character {
  id: string;
  bookId: string;
  name: string;
  description: string | null;
  role: string | null;
  notes: string | null;
  appearance: string | null;
  personality: string | null;
  backstory: string | null;
  goals: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface PlotPoint {
  id: string;
  bookId: string;
  title: string;
  description: string | null;
  chapterId: string | null;
  sceneId: string | null;
  completed: boolean;
  order: number; // Keep as 'order' for compatibility, map to 'orderIndex' in Firebase
  orderIndex: number; // Firebase field name
  createdAt: string;
  updatedAt: string;
}

export interface BookSettings {
  backgroundImage?: string;
  fontSize: number;
  fontFamily: 'serif' | 'sans';
  theme: 'light' | 'dark';
  lineHeight: number;
  paragraphSpacing: number;
}

export interface WritingSession {
  id: string;
  bookId: string;
  userId: string;
  date: string; // YYYY-MM-DD format
  wordsWritten: number;
  timeSpent: number; // in minutes
  createdAt: string;
}

export interface WritingGoals {
  weekly: number;
}

export interface WritingStats {
  thisWeek: number;
}

export type ViewMode = 'writing' | 'characters' | 'plot' | 'worldbuilding' | 'settings' | 'statistics';

export interface Worldbuilding {
  locations: Location[];
  institutions: Institution[];
  politics: PoliticalSystem[];
  hierarchies: Hierarchy[];
  cultures: Culture[];
  religions: Religion[];
  languages: Language[];
  technologies: Technology[];
  economies: Economy[];
  conflicts: Conflict[];
}

export interface Location {
  id: string;
  bookId: string;
  name: string;
  type: 'continent' | 'country' | 'city' | 'town' | 'village' | 'landmark' | 'building' | 'room' | 'other';
  description: string | null;
  geography: string | null;
  climate: string | null;
  population: string | null;
  government: string | null;
  economy: string | null;
  culture: string | null;
  history: string | null;
  parentLocationId: string | null;
  coordinates: { x: number; y: number } | null;
  mapImageUrl: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Institution {
  id: string;
  bookId: string;
  name: string;
  type: 'government' | 'military' | 'religious' | 'educational' | 'commercial' | 'guild' | 'organization' | 'other';
  description: string | null;
  purpose: string | null;
  structure: string | null;
  leadership: string | null;
  membership: string | null;
  influence: 'local' | 'regional' | 'national' | 'international' | 'global';
  resources: string | null;
  history: string | null;
  locationId: string | null;
  allies: string | null;
  enemies: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface PoliticalSystem {
  id: string;
  bookId: string;
  name: string;
  type: 'monarchy' | 'democracy' | 'republic' | 'dictatorship' | 'theocracy' | 'oligarchy' | 'anarchy' | 'other';
  description: string | null;
  structure: string | null;
  leadership: string | null;
  laws: string | null;
  enforcement: string | null;
  territory: string | null;
  relations: string | null;
  history: string | null;
  locationId: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Hierarchy {
  id: string;
  bookId: string;
  name: string;
  type: 'social' | 'military' | 'religious' | 'political' | 'economic' | 'academic' | 'other';
  description: string | null;
  levels: HierarchyLevel[];
  mobility: 'none' | 'limited' | 'moderate' | 'high';
  basis: string | null;
  privileges: string | null;
  responsibilities: string | null;
  locationId: string | null;
  institutionId: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface HierarchyLevel {
  id: string;
  name: string;
  rank: number;
  description: string | null;
  requirements: string | null;
  privileges: string | null;
  responsibilities: string | null;
}

export interface Culture {
  id: string;
  bookId: string;
  name: string;
  description: string | null;
  values: string | null;
  traditions: string | null;
  customs: string | null;
  arts: string | null;
  cuisine: string | null;
  clothing: string | null;
  architecture: string | null;
  language: string | null;
  religion: string | null;
  locationId: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Religion {
  id: string;
  bookId: string;
  name: string;
  type: 'monotheistic' | 'polytheistic' | 'pantheistic' | 'animistic' | 'philosophical' | 'other';
  description: string | null;
  beliefs: string | null;
  practices: string | null;
  clergy: string | null;
  temples: string | null;
  holidays: string | null;
  texts: string | null;
  symbols: string | null;
  followers: string | null;
  influence: 'local' | 'regional' | 'national' | 'international' | 'global';
  locationId: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Language {
  id: string;
  bookId: string;
  name: string;
  type: 'spoken' | 'written' | 'sign' | 'magical' | 'ancient' | 'other';
  description: string | null;
  speakers: string | null;
  regions: string | null;
  script: string | null;
  grammar: string | null;
  vocabulary: string | null;
  dialects: string | null;
  history: string | null;
  status: 'living' | 'dead' | 'endangered' | 'constructed';
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Technology {
  id: string;
  bookId: string;
  name: string;
  type: 'transportation' | 'communication' | 'military' | 'medical' | 'agricultural' | 'industrial' | 'magical' | 'other';
  description: string | null;
  function: string | null;
  materials: string | null;
  availability: 'common' | 'uncommon' | 'rare' | 'unique';
  cost: string | null;
  requirements: string | null;
  limitations: string | null;
  inventor: string | null;
  history: string | null;
  locationId: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Economy {
  id: string;
  bookId: string;
  name: string;
  type: 'agricultural' | 'industrial' | 'service' | 'trade' | 'resource' | 'mixed' | 'other';
  description: string | null;
  currency: string | null;
  majorIndustries: string | null;
  tradeRoutes: string | null;
  resources: string | null;
  laborForce: string | null;
  wealth: 'poor' | 'modest' | 'comfortable' | 'wealthy' | 'extremely wealthy';
  inequality: 'low' | 'moderate' | 'high' | 'extreme';
  locationId: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Conflict {
  id: string;
  bookId: string;
  name: string;
  type: 'war' | 'rebellion' | 'civil war' | 'trade dispute' | 'territorial' | 'religious' | 'personal' | 'other';
  description: string | null;
  parties: string | null;
  causes: string | null;
  timeline: string | null;
  battles: string | null;
  consequences: string | null;
  resolution: string | null;
  status: 'brewing' | 'active' | 'resolved' | 'ongoing';
  locationId: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}</content>