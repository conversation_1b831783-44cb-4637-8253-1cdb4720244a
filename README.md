# WriterOne

A modern writing application with document sharing and collaboration features.

## Features

### Document Sharing and Editing

WriterOne allows you to share your documents with other users for editing and feedback:

- **Share Documents**: Send your current work to another user for editing or review
- **Permission Control**: Choose between view-only or editing permissions
- **Comments**: Add comments to specific parts of the document
- **Track Changes**: See what changes were made by editors
- **Export to PDF**: Export the document with comments to PDF format
- **Return Documents**: Editors can return documents with their changes and comments

### How to Use Document Sharing

1. Click the "Share Document" button in the sidebar
2. Enter the recipient's email address
3. Choose permission level (view or edit)
4. Add an optional message
5. Click "Share Document"

The recipient will receive access to the document and can view or edit it based on the permissions you set.

## Technical Implementation

The document sharing feature uses Firebase's free tier:

- **Firebase Authentication**: For user identification
- **Cloud Firestore**: For storing documents, comments, and sharing metadata
- **Security Rules**: To ensure proper access control