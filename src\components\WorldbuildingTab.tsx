import React, { useState } from 'react';
import { 
  Map, Building, Crown, Users, Palette, Church, Languages, Cpu, 
  DollarSign, Swords, Plus, Edit, Trash2, ChevronDown, ChevronRight,
  MapPin, Globe, Home, Landmark
} from 'lucide-react';
import { 
  Book, Location, Institution, PoliticalSystem, Hierarchy, Culture, 
  Religion, Language, Technology, Economy, Conflict, HierarchyLevel 
} from '../types';
import {
  createNewLocation, createNewInstitution, createNewPoliticalSystem,
  createNewHierarchy, createNewCulture, createNewReligion, createNewLanguage,
  createNewTechnology, createNewEconomy, createNewConflict, createNewHierarchyLevel,
  updateWorldbuildingElement, addWorldbuildingElement
} from '../utils/bookUtils';

interface WorldbuildingTabProps {
  book: Book;
  onUpdateBook: (book: Book) => void;
  glassClass: string;
  glassClassLight: string;
}

type WorldbuildingCategory = 'locations' | 'institutions' | 'politics' | 'hierarchies' | 
                           'cultures' | 'religions' | 'languages' | 'technologies' | 
                           'economies' | 'conflicts';

export const WorldbuildingTab: React.FC<WorldbuildingTabProps> = ({
  book,
  onUpdateBook,
  glassClass,
  glassClassLight
}) => {
  const [activeCategory, setActiveCategory] = useState<WorldbuildingCategory>('locations');
  const [showNewForm, setShowNewForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [newItemName, setNewItemName] = useState('');

  const categories = [
    { key: 'locations' as const, label: 'Locations & Maps', icon: Map, color: 'text-green-400' },
    { key: 'institutions' as const, label: 'Institutions', icon: Building, color: 'text-blue-400' },
    { key: 'politics' as const, label: 'Political Systems', icon: Crown, color: 'text-purple-400' },
    { key: 'hierarchies' as const, label: 'Hierarchies', icon: Users, color: 'text-orange-400' },
    { key: 'cultures' as const, label: 'Cultures', icon: Palette, color: 'text-pink-400' },
    { key: 'religions' as const, label: 'Religions', icon: Church, color: 'text-yellow-400' },
    { key: 'languages' as const, label: 'Languages', icon: Languages, color: 'text-indigo-400' },
    { key: 'technologies' as const, label: 'Technologies', icon: Cpu, color: 'text-cyan-400' },
    { key: 'economies' as const, label: 'Economies', icon: DollarSign, color: 'text-emerald-400' },
    { key: 'conflicts' as const, label: 'Conflicts', icon: Swords, color: 'text-red-400' }
  ];

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const getSingularLabel = (label: string) => {
    const singularMap: { [key: string]: string } = {
      'Technologies': 'Technology',
      'Economies': 'Economy',
      'Hierarchies': 'Hierarchy'
    };
    return singularMap[label] || label.slice(0, -1);
  };

  const handleCreateNew = () => {
    if (!newItemName.trim()) return;

    const baseElement = {
      bookId: book.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    let newElement;
    switch (activeCategory) {
      case 'locations':
        newElement = { ...createNewLocation(newItemName.trim()), ...baseElement };
        break;
      case 'institutions':
        newElement = { ...createNewInstitution(newItemName.trim()), ...baseElement };
        break;
      case 'politics':
        newElement = { ...createNewPoliticalSystem(newItemName.trim()), ...baseElement };
        break;
      case 'hierarchies':
        newElement = { ...createNewHierarchy(newItemName.trim()), ...baseElement };
        break;
      case 'cultures':
        newElement = { ...createNewCulture(newItemName.trim()), ...baseElement };
        break;
      case 'religions':
        newElement = { ...createNewReligion(newItemName.trim()), ...baseElement };
        break;
      case 'languages':
        newElement = { ...createNewLanguage(newItemName.trim()), ...baseElement };
        break;
      case 'technologies':
        newElement = { ...createNewTechnology(newItemName.trim()), ...baseElement };
        break;
      case 'economies':
        newElement = { ...createNewEconomy(newItemName.trim()), ...baseElement };
        break;
      case 'conflicts':
        newElement = { ...createNewConflict(newItemName.trim()), ...baseElement };
        break;
    }

    const updatedBook = addWorldbuildingElement(book, activeCategory, newElement);
    onUpdateBook(updatedBook);
    setNewItemName('');
    setShowNewForm(false);
    setExpandedItems(new Set([newElement.id]));
    setEditingId(newElement.id);
  };

  const handleUpdate = (id: string, updates: any) => {
    const updatedBook = updateWorldbuildingElement(book, activeCategory, id, updates);
    onUpdateBook(updatedBook);
  };

  const handleDelete = (id: string, name: string) => {
    if (window.confirm(`Are you sure you want to delete "${name}"?`)) {
      const updatedBook = {
        ...book,
        worldbuilding: {
          ...book.worldbuilding,
          [activeCategory]: book.worldbuilding[activeCategory].filter((item: any) => item.id !== id)
        },
        updatedAt: new Date().toISOString()
      };
      onUpdateBook(updatedBook);
    }
  };

  const renderLocationForm = (location?: Location) => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <input
          type="text"
          placeholder="Location name"
          defaultValue={location?.name || ''}
          onChange={(e) => location && handleUpdate(location.id, { name: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
        />
        <select
          defaultValue={location?.type || 'other'}
          onChange={(e) => location && handleUpdate(location.id, { type: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white focus:outline-none focus:ring-2 focus:ring-white/50"
        >
          <option value="continent">Continent</option>
          <option value="country">Country</option>
          <option value="city">City</option>
          <option value="town">Town</option>
          <option value="village">Village</option>
          <option value="landmark">Landmark</option>
          <option value="building">Building</option>
          <option value="room">Room</option>
          <option value="other">Other</option>
        </select>
      </div>
      <textarea
        placeholder="Description"
        defaultValue={location?.description || ''}
        onChange={(e) => location && handleUpdate(location.id, { description: e.target.value })}
        className="w-full px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
        rows={3}
      />
      <div className="grid grid-cols-2 gap-4">
        <textarea
          placeholder="Geography"
          defaultValue={location?.geography || ''}
          onChange={(e) => location && handleUpdate(location.id, { geography: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
          rows={2}
        />
        <textarea
          placeholder="Climate"
          defaultValue={location?.climate || ''}
          onChange={(e) => location && handleUpdate(location.id, { climate: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
          rows={2}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <input
          type="text"
          placeholder="Population"
          defaultValue={location?.population || ''}
          onChange={(e) => location && handleUpdate(location.id, { population: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
        />
        <input
          type="text"
          placeholder="Government"
          defaultValue={location?.government || ''}
          onChange={(e) => location && handleUpdate(location.id, { government: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
        />
      </div>
      <textarea
        placeholder="History"
        defaultValue={location?.history || ''}
        onChange={(e) => location && handleUpdate(location.id, { history: e.target.value })}
        className="w-full px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
        rows={3}
      />
      <textarea
        placeholder="Notes"
        defaultValue={location?.notes || ''}
        onChange={(e) => location && handleUpdate(location.id, { notes: e.target.value })}
        className="w-full px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
        rows={2}
      />
    </div>
  );

  const renderInstitutionForm = (institution?: Institution) => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <input
          type="text"
          placeholder="Institution name"
          defaultValue={institution?.name || ''}
          onChange={(e) => institution && handleUpdate(institution.id, { name: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
        />
        <select
          defaultValue={institution?.type || 'other'}
          onChange={(e) => institution && handleUpdate(institution.id, { type: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white focus:outline-none focus:ring-2 focus:ring-white/50"
        >
          <option value="government">Government</option>
          <option value="military">Military</option>
          <option value="religious">Religious</option>
          <option value="educational">Educational</option>
          <option value="commercial">Commercial</option>
          <option value="guild">Guild</option>
          <option value="organization">Organization</option>
          <option value="other">Other</option>
        </select>
      </div>
      <textarea
        placeholder="Description"
        defaultValue={institution?.description || ''}
        onChange={(e) => institution && handleUpdate(institution.id, { description: e.target.value })}
        className="w-full px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
        rows={3}
      />
      <div className="grid grid-cols-2 gap-4">
        <textarea
          placeholder="Purpose"
          defaultValue={institution?.purpose || ''}
          onChange={(e) => institution && handleUpdate(institution.id, { purpose: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
          rows={2}
        />
        <textarea
          placeholder="Structure"
          defaultValue={institution?.structure || ''}
          onChange={(e) => institution && handleUpdate(institution.id, { structure: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
          rows={2}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <input
          type="text"
          placeholder="Leadership"
          defaultValue={institution?.leadership || ''}
          onChange={(e) => institution && handleUpdate(institution.id, { leadership: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
        />
        <select
          defaultValue={institution?.influence || 'local'}
          onChange={(e) => institution && handleUpdate(institution.id, { influence: e.target.value })}
          className="px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white focus:outline-none focus:ring-2 focus:ring-white/50"
        >
          <option value="local">Local</option>
          <option value="regional">Regional</option>
          <option value="national">National</option>
          <option value="international">International</option>
          <option value="global">Global</option>
        </select>
      </div>
    </div>
  );

  const renderGenericForm = (item: any, fields: Array<{key: string, label: string, type?: string, options?: string[]}>) => (
    <div className="space-y-4">
      {fields.map(field => (
        <div key={field.key}>
          {field.type === 'select' ? (
            <select
              defaultValue={item?.[field.key] || (field.options?.[0] || '')}
              onChange={(e) => item && handleUpdate(item.id, { [field.key]: e.target.value })}
              className="w-full px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white focus:outline-none focus:ring-2 focus:ring-white/50"
            >
              {field.options?.map(option => (
                <option key={option} value={option}>{option.charAt(0).toUpperCase() + option.slice(1)}</option>
              ))}
            </select>
          ) : field.type === 'textarea' ? (
            <textarea
              placeholder={field.label}
              defaultValue={item?.[field.key] || ''}
              onChange={(e) => item && handleUpdate(item.id, { [field.key]: e.target.value })}
              className="w-full px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
              rows={3}
            />
          ) : (
            <input
              type="text"
              placeholder={field.label}
              defaultValue={item?.[field.key] || ''}
              onChange={(e) => item && handleUpdate(item.id, { [field.key]: e.target.value })}
              className="w-full px-3 py-2 bg-black/50 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
            />
          )}
        </div>
      ))}
    </div>
  );

  const getFormFields = (category: WorldbuildingCategory) => {
    switch (category) {
      case 'politics':
        return [
          { key: 'name', label: 'Name' },
          { key: 'type', label: 'Type', type: 'select', options: ['monarchy', 'democracy', 'republic', 'dictatorship', 'theocracy', 'oligarchy', 'anarchy', 'other'] },
          { key: 'description', label: 'Description', type: 'textarea' },
          { key: 'structure', label: 'Structure', type: 'textarea' },
          { key: 'leadership', label: 'Leadership' },
          { key: 'laws', label: 'Laws', type: 'textarea' }
        ];
      case 'cultures':
        return [
          { key: 'name', label: 'Name' },
          { key: 'description', label: 'Description', type: 'textarea' },
          { key: 'values', label: 'Values', type: 'textarea' },
          { key: 'traditions', label: 'Traditions', type: 'textarea' },
          { key: 'arts', label: 'Arts & Entertainment', type: 'textarea' },
          { key: 'cuisine', label: 'Cuisine' }
        ];
      case 'religions':
        return [
          { key: 'name', label: 'Name' },
          { key: 'type', label: 'Type', type: 'select', options: ['monotheistic', 'polytheistic', 'pantheistic', 'animistic', 'philosophical', 'other'] },
          { key: 'description', label: 'Description', type: 'textarea' },
          { key: 'beliefs', label: 'Beliefs', type: 'textarea' },
          { key: 'practices', label: 'Practices', type: 'textarea' },
          { key: 'clergy', label: 'Clergy' }
        ];
      case 'languages':
        return [
          { key: 'name', label: 'Name' },
          { key: 'type', label: 'Type', type: 'select', options: ['spoken', 'written', 'sign', 'magical', 'ancient', 'other'] },
          { key: 'description', label: 'Description', type: 'textarea' },
          { key: 'speakers', label: 'Speakers' },
          { key: 'regions', label: 'Regions' },
          { key: 'status', label: 'Status', type: 'select', options: ['living', 'dead', 'endangered', 'constructed'] }
        ];
      case 'technologies':
        return [
          { key: 'name', label: 'Name' },
          { key: 'type', label: 'Type', type: 'select', options: ['transportation', 'communication', 'military', 'medical', 'agricultural', 'industrial', 'magical', 'other'] },
          { key: 'description', label: 'Description', type: 'textarea' },
          { key: 'function', label: 'Function', type: 'textarea' },
          { key: 'availability', label: 'Availability', type: 'select', options: ['common', 'uncommon', 'rare', 'unique'] },
          { key: 'inventor', label: 'Inventor' }
        ];
      case 'economies':
        return [
          { key: 'name', label: 'Name' },
          { key: 'type', label: 'Type', type: 'select', options: ['agricultural', 'industrial', 'service', 'trade', 'resource', 'mixed', 'other'] },
          { key: 'description', label: 'Description', type: 'textarea' },
          { key: 'currency', label: 'Currency' },
          { key: 'majorIndustries', label: 'Major Industries', type: 'textarea' },
          { key: 'wealth', label: 'Wealth Level', type: 'select', options: ['poor', 'modest', 'comfortable', 'wealthy', 'extremely wealthy'] }
        ];
      case 'conflicts':
        return [
          { key: 'name', label: 'Name' },
          { key: 'type', label: 'Type', type: 'select', options: ['war', 'rebellion', 'civil war', 'trade dispute', 'territorial', 'religious', 'personal', 'other'] },
          { key: 'description', label: 'Description', type: 'textarea' },
          { key: 'parties', label: 'Parties Involved', type: 'textarea' },
          { key: 'causes', label: 'Causes', type: 'textarea' },
          { key: 'status', label: 'Status', type: 'select', options: ['brewing', 'active', 'resolved', 'ongoing'] }
        ];
      case 'hierarchies':
        return [
          { key: 'name', label: 'Name' },
          { key: 'type', label: 'Type', type: 'select', options: ['social', 'military', 'religious', 'political', 'economic', 'academic', 'other'] },
          { key: 'description', label: 'Description', type: 'textarea' },
          { key: 'mobility', label: 'Social Mobility', type: 'select', options: ['none', 'limited', 'moderate', 'high'] },
          { key: 'basis', label: 'Basis of Hierarchy', type: 'textarea' }
        ];
      default:
        return [];
    }
  };

  const renderItemCard = (item: any, category: WorldbuildingCategory) => {
    const isEditing = editingId === item.id;

    return (
      <div key={item.id} className={`${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 shadow-sm`}>
        <div className="p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <h3 className="text-lg font-serif font-semibold text-white">{item.name}</h3>
              {item.type && (
                <span className="ml-2 px-2 py-1 bg-white/20 rounded text-xs text-white/80 font-sans">
                  {item.type}
                </span>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setEditingId(isEditing ? null : item.id)}
                className="text-white/60 hover:text-white"
              >
                <Edit className="w-4 h-4" />
              </button>
              <button
                onClick={() => handleDelete(item.id, item.name)}
                className="text-white/60 hover:text-red-400"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>

          {item.description && !isEditing && (
            <p className="text-white/80 font-sans text-sm mb-2">{item.description}</p>
          )}

          <div className="mt-4 pt-4 border-t border-white/20">
            {isEditing ? (
              <div>
                {category === 'locations' && renderLocationForm(item)}
                {category === 'institutions' && renderInstitutionForm(item)}
                {!['locations', 'institutions'].includes(category) && renderGenericForm(item, getFormFields(category))}
                <div className="flex space-x-3 mt-4">
                  <button
                    onClick={() => setEditingId(null)}
                    className="px-4 py-2 bg-black/50 backdrop-blur-md hover:bg-black/60 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
                  >
                    Save Changes
                  </button>
                  <button
                    onClick={() => setEditingId(null)}
                    className="px-4 py-2 bg-black/40 hover:bg-black/50 text-white/80 font-sans font-medium rounded-lg border border-white/10 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-2 text-sm">
                {Object.entries(item).map(([key, value]) => {
                  if (['id', 'bookId', 'createdAt', 'updatedAt', 'name', 'description'].includes(key) || !value) return null;
                  return (
                    <div key={key} className="flex">
                      <span className="font-medium text-white/90 capitalize w-24 flex-shrink-0">
                        {key.replace(/([A-Z])/g, ' $1').trim()}:
                      </span>
                      <span className="text-white/70 font-sans">{String(value)}</span>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const currentCategory = categories.find(cat => cat.key === activeCategory);
  const items = book.worldbuilding[activeCategory] || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-serif font-semibold text-white flex items-center">
          {currentCategory && <currentCategory.icon className={`w-6 h-6 mr-2 ${currentCategory.color}`} />}
          Worldbuilding
        </h2>
        <button
          onClick={() => setShowNewForm(true)}
          className="flex items-center px-4 py-2 bg-black/50 backdrop-blur-md hover:bg-black/60 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add {getSingularLabel(currentCategory?.label || '')}
        </button>
      </div>

      {/* Category Navigation */}
      <div className="bg-black/60 backdrop-blur-md rounded-xl border border-white/20 p-4">
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {categories.map((category) => (
            <button
              key={category.key}
              onClick={() => setActiveCategory(category.key)}
              className={`flex items-center p-3 rounded-lg text-left transition-colors ${
                activeCategory === category.key
                  ? 'bg-black/70 text-white/95'
                  : 'text-white/80 hover:bg-black/50 hover:text-white'
              }`}
            >
              <category.icon className={`w-4 h-4 mr-2 ${category.color}`} />
              <span className="font-sans text-sm">{category.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* New Item Form */}
      {showNewForm && (
        <div className={`p-6 ${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 shadow-sm`}>
          <input
            type="text"
            value={newItemName}
            onChange={(e) => setNewItemName(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleCreateNew()}
            placeholder={`${getSingularLabel(currentCategory?.label || '')} name...`}
            className="w-full px-4 py-3 bg-black/50 border border-white/20 rounded-lg font-sans text-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
            autoFocus
          />
          <div className="flex space-x-3 mt-4">
            <button
              onClick={handleCreateNew}
              className="px-6 py-2 bg-black/50 backdrop-blur-md hover:bg-black/60 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
            >
              Create {getSingularLabel(currentCategory?.label || '')}
            </button>
            <button
              onClick={() => setShowNewForm(false)}
              className="px-6 py-2 bg-black/40 hover:bg-black/50 text-white/80 font-sans font-medium rounded-lg border border-white/10 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Items List */}
      <div className="space-y-4">
        {items.length === 0 ? (
          <div className={`p-8 ${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 text-center`}>
            <currentCategory.icon className={`w-12 h-12 mx-auto mb-4 ${currentCategory.color} opacity-50`} />
            <p className="text-white/60 font-sans">No {currentCategory?.label.toLowerCase()} created yet.</p>
            <p className="text-white/40 font-sans text-sm mt-1">Click "Add {getSingularLabel(currentCategory?.label || '')}" to get started.</p>
          </div>
        ) : (
          items.map((item: any) => renderItemCard(item, activeCategory))
        )}
      </div>
    </div>
  );
};