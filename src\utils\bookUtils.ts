import { v4 as uuidv4 } from 'uuid';
import { Book, Chapter, Scene, Character, PlotPoint, WritingSession, ProjectType, Worldbuilding, Location, Institution, PoliticalSystem, Hierarchy, Culture, Religion, Language, Technology, Economy, Conflict, HierarchyLevel } from '../types';
import { getProjectTemplate } from './projectTemplates';

export const createNewBook = (title: string, author: string, projectType: ProjectType = 'novel', ownerId?: string): Book => {
  const now = new Date().toISOString();
  const bookId = generateId();
  const template = getProjectTemplate(projectType);

  // Create chapters with proper IDs and bookId
  const chapters: Chapter[] = template.chapters.map((chapterTemplate, index) => ({
    ...chapterTemplate,
    id: generateId(),
    bookId,
    createdAt: now,
    updatedAt: now
  }));

  // Create characters with proper IDs and bookId
  const characters: Character[] = template.characters.map((characterTemplate) => ({
    ...characterTemplate,
    id: generateId(),
    bookId,
    createdAt: now,
    updatedAt: now
  }));

  // Create plot points with proper IDs and bookId
  const plotPoints: PlotPoint[] = template.plotPoints.map((plotTemplate) => ({
    ...plotTemplate,
    id: generateId(),
    bookId,
    createdAt: now,
    updatedAt: now
  }));

  return {
    id: bookId,
    title,
    author,
    projectType,
    ownerId: ownerId || '',
    collaborators: [],
    createdAt: now,
    updatedAt: now,
    wordCount: 0,
    chapters,
    characters,
    plotPoints,
    worldbuilding: createEmptyWorldbuilding(),
    settings: {
      fontSize: 16,
      fontFamily: 'serif',
      theme: 'light',
      lineHeight: 1.6,
      paragraphSpacing: 1.2,
    },
  };
};

export const createNewChapter = (title: string, order: number): Chapter => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    title,
    content: '',
    wordCount: 0,
    scenes: [],
    order,
    createdAt: now,
    updatedAt: now,
  };
};

export const createNewScene = (title: string, order: number): Scene => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    title,
    content: '',
    wordCount: 0,
    order,
    createdAt: now,
    updatedAt: now,
  };
};

export const createNewCharacter = (name: string): Character => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    name,
    description: '',
    role: '',
    notes: '',
    appearance: '',
    personality: '',
    backstory: '',
    goals: '',
    createdAt: now,
    updatedAt: now,
  };
};

export const createNewPlotPoint = (title: string, order: number): PlotPoint => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    title,
    description: '',
    completed: false,
    order,
    createdAt: now,
    updatedAt: now,
  };
};

export const generateId = (): string => {
  return uuidv4();
};

export const countWords = (text: string): number => {
  if (!text || text.trim() === '') return 0;
  // Remove HTML tags and count words
  const plainText = text.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
  return plainText.split(' ').filter(word => word.length > 0).length;
};

export const updateBookWordCount = (book: Book): Book => {
  const totalWords = book.chapters.reduce((total, chapter) => {
    const chapterWords = chapter.scenes.reduce((sceneTotal, scene) => {
      return sceneTotal + countWords(scene.content);
    }, countWords(chapter.content));
    return total + chapterWords;
  }, 0);

  return {
    ...book,
    wordCount: totalWords,
    updatedAt: new Date().toISOString(),
  };
};

export const deleteChapter = (book: Book, chapterId: string): Book => {
  return {
    ...book,
    chapters: book.chapters.filter(chapter => chapter.id !== chapterId),
    updatedAt: new Date().toISOString(),
  };
};

export const deleteScene = (book: Book, chapterId: string, sceneId: string): Book => {
  return {
    ...book,
    chapters: book.chapters.map(chapter => 
      chapter.id === chapterId 
        ? { ...chapter, scenes: chapter.scenes.filter(scene => scene.id !== sceneId) }
        : chapter
    ),
    updatedAt: new Date().toISOString(),
  };
};

export const deleteCharacter = (book: Book, characterId: string): Book => {
  return {
    ...book,
    characters: book.characters.filter(character => character.id !== characterId),
    updatedAt: new Date().toISOString(),
  };
};

export const deletePlotPoint = (book: Book, plotPointId: string): Book => {
  return {
    ...book,
    plotPoints: book.plotPoints.filter(plotPoint => plotPoint.id !== plotPointId),
    updatedAt: new Date().toISOString(),
  };
};

// Worldbuilding deletion functions
export const deleteLocation = (book: Book, locationId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      locations: book.worldbuilding.locations.filter(location => location.id !== locationId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const deleteInstitution = (book: Book, institutionId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      institutions: book.worldbuilding.institutions.filter(institution => institution.id !== institutionId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const deletePoliticalSystem = (book: Book, politicsId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      politics: book.worldbuilding.politics.filter(politics => politics.id !== politicsId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const deleteHierarchy = (book: Book, hierarchyId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      hierarchies: book.worldbuilding.hierarchies.filter(hierarchy => hierarchy.id !== hierarchyId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const deleteCulture = (book: Book, cultureId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      cultures: book.worldbuilding.cultures.filter(culture => culture.id !== cultureId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const deleteReligion = (book: Book, religionId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      religions: book.worldbuilding.religions.filter(religion => religion.id !== religionId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const deleteLanguage = (book: Book, languageId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      languages: book.worldbuilding.languages.filter(language => language.id !== languageId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const deleteTechnology = (book: Book, technologyId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      technologies: book.worldbuilding.technologies.filter(technology => technology.id !== technologyId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const deleteEconomy = (book: Book, economyId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      economies: book.worldbuilding.economies.filter(economy => economy.id !== economyId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const deleteConflict = (book: Book, conflictId: string): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      conflicts: book.worldbuilding.conflicts.filter(conflict => conflict.id !== conflictId)
    },
    updatedAt: new Date().toISOString(),
  };
};

export const reorderChapters = (book: Book, startIndex: number, endIndex: number): Book => {
  const chapters = Array.from(book.chapters);
  const [reorderedItem] = chapters.splice(startIndex, 1);
  chapters.splice(endIndex, 0, reorderedItem);
  
  // Update order numbers
  const updatedChapters = chapters.map((chapter, index) => ({
    ...chapter,
    order: index,
    updatedAt: new Date().toISOString(),
  }));

  return {
    ...book,
    chapters: updatedChapters,
    updatedAt: new Date().toISOString(),
  };
};

export const reorderScenes = (book: Book, chapterId: string, startIndex: number, endIndex: number): Book => {
  return {
    ...book,
    chapters: book.chapters.map(chapter => {
      if (chapter.id !== chapterId) return chapter;
      
      const scenes = Array.from(chapter.scenes);
      const [reorderedItem] = scenes.splice(startIndex, 1);
      scenes.splice(endIndex, 0, reorderedItem);
      
      // Update order numbers
      const updatedScenes = scenes.map((scene, index) => ({
        ...scene,
        order: index,
        updatedAt: new Date().toISOString(),
      }));

      return {
        ...chapter,
        scenes: updatedScenes,
        updatedAt: new Date().toISOString(),
      };
    }),
    updatedAt: new Date().toISOString(),
  };
};

// Worldbuilding update functions
export const updateWorldbuildingElement = <T extends { id: string; updatedAt: string }>(
  book: Book,
  category: keyof Worldbuilding,
  elementId: string,
  updates: Partial<T>
): Book => {
  const now = new Date().toISOString();
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      [category]: (book.worldbuilding[category] as T[]).map(element =>
        element.id === elementId
          ? { ...element, ...updates, updatedAt: now }
          : element
      )
    },
    updatedAt: now
  };
};

export const addWorldbuildingElement = <T>(
  book: Book,
  category: keyof Worldbuilding,
  element: T
): Book => {
  return {
    ...book,
    worldbuilding: {
      ...book.worldbuilding,
      [category]: [...(book.worldbuilding[category] as T[]), element]
    },
    updatedAt: new Date().toISOString()
  };
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// Helper function to get worldbuilding statistics
export const getWorldbuildingStatistics = (worldbuilding: Worldbuilding) => {
  return {
    totalLocations: worldbuilding.locations.length,
    totalInstitutions: worldbuilding.institutions.length,
    totalPolitics: worldbuilding.politics.length,
    totalHierarchies: worldbuilding.hierarchies.length,
    totalCultures: worldbuilding.cultures.length,
    totalReligions: worldbuilding.religions.length,
    totalLanguages: worldbuilding.languages.length,
    totalTechnologies: worldbuilding.technologies.length,
    totalEconomies: worldbuilding.economies.length,
    totalConflicts: worldbuilding.conflicts.length,
    totalElements: worldbuilding.locations.length + worldbuilding.institutions.length + 
                  worldbuilding.politics.length + worldbuilding.hierarchies.length +
                  worldbuilding.cultures.length + worldbuilding.religions.length +
                  worldbuilding.languages.length + worldbuilding.technologies.length +
                  worldbuilding.economies.length + worldbuilding.conflicts.length
  };
};

export const getWritingStatistics = (book: Book): {
  totalWords: number;
  totalChapters: number;
  totalScenes: number;
  averageWordsPerChapter: number;
  averageWordsPerScene: number;
  lastUpdated: string;
} => {
  const totalChapters = book.chapters.length;
  const totalScenes = book.chapters.reduce((total, chapter) => total + chapter.scenes.length, 0);
  const totalWords = book.wordCount;
  
  return {
    totalWords,
    totalChapters,
    totalScenes,
    averageWordsPerChapter: totalChapters > 0 ? Math.round(totalWords / totalChapters) : 0,
    averageWordsPerScene: totalScenes > 0 ? Math.round(totalWords / totalScenes) : 0,
    lastUpdated: book.updatedAt,
  };
};

// Worldbuilding utility functions
export const createEmptyWorldbuilding = (): Worldbuilding => ({
  locations: [],
  institutions: [],
  politics: [],
  hierarchies: [],
  cultures: [],
  religions: [],
  languages: [],
  technologies: [],
  economies: [],
  conflicts: []
});

export const createNewLocation = (name: string, type: Location['type'] = 'other'): Location => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    type,
    description: null,
    geography: null,
    climate: null,
    population: null,
    government: null,
    economy: null,
    culture: null,
    history: null,
    parentLocationId: null,
    coordinates: null,
    mapImageUrl: null,
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewInstitution = (name: string, type: Institution['type'] = 'other'): Institution => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    type,
    description: null,
    purpose: null,
    structure: null,
    leadership: null,
    membership: null,
    influence: 'local',
    resources: null,
    history: null,
    locationId: null,
    allies: null,
    enemies: null,
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewPoliticalSystem = (name: string, type: PoliticalSystem['type'] = 'other'): PoliticalSystem => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    type,
    description: null,
    structure: null,
    leadership: null,
    laws: null,
    enforcement: null,
    territory: null,
    relations: null,
    history: null,
    locationId: null,
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewHierarchy = (name: string, type: Hierarchy['type'] = 'other'): Hierarchy => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    type,
    description: null,
    levels: [],
    mobility: 'moderate',
    basis: null,
    privileges: null,
    responsibilities: null,
    locationId: null,
    institutionId: null,
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewCulture = (name: string): Culture => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    description: null,
    values: null,
    traditions: null,
    customs: null,
    arts: null,
    cuisine: null,
    clothing: null,
    architecture: null,
    language: null,
    religion: null,
    locationId: null,
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewReligion = (name: string, type: Religion['type'] = 'other'): Religion => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    type,
    description: null,
    beliefs: null,
    practices: null,
    clergy: null,
    temples: null,
    holidays: null,
    texts: null,
    symbols: null,
    followers: null,
    influence: 'local',
    locationId: null,
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewLanguage = (name: string, type: Language['type'] = 'spoken'): Language => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    type,
    description: null,
    speakers: null,
    regions: null,
    script: null,
    grammar: null,
    vocabulary: null,
    dialects: null,
    history: null,
    status: 'living',
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewTechnology = (name: string, type: Technology['type'] = 'other'): Technology => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    type,
    description: null,
    function: null,
    materials: null,
    availability: 'common',
    cost: null,
    requirements: null,
    limitations: null,
    inventor: null,
    history: null,
    locationId: null,
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewEconomy = (name: string, type: Economy['type'] = 'mixed'): Economy => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    type,
    description: null,
    currency: null,
    majorIndustries: null,
    tradeRoutes: null,
    resources: null,
    laborForce: null,
    wealth: 'modest',
    inequality: 'moderate',
    locationId: null,
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewConflict = (name: string, type: Conflict['type'] = 'other'): Conflict => {
  const now = new Date().toISOString();
  return {
    id: generateId(),
    bookId: '',
    name,
    type,
    description: null,
    parties: null,
    causes: null,
    timeline: null,
    battles: null,
    consequences: null,
    resolution: null,
    status: 'brewing',
    locationId: null,
    notes: null,
    createdAt: now,
    updatedAt: now
  };
};

export const createNewHierarchyLevel = (name: string, rank: number): HierarchyLevel => ({
  id: generateId(),
  name,
  rank,
  description: null,
  requirements: null,
  privileges: null,
  responsibilities: null
});