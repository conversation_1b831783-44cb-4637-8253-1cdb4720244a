import { Book } from '../types';
import { createEmptyWorldbuilding } from './bookUtils';

/**
 * Migrates a book to include worldbuilding structure if it doesn't exist
 */
export const migrateBookToWorldbuilding = (book: Book): Book => {
  // Check if worldbuilding exists, if not, add it
  if (!book.worldbuilding) {
    return {
      ...book,
      worldbuilding: createEmptyWorldbuilding(),
      updatedAt: new Date().toISOString()
    };
  }
  
  return book;
};

/**
 * Migrates an array of books to include worldbuilding structure
 */
export const migrateBooksToWorldbuilding = (books: Book[]): Book[] => {
  return books.map(migrateBookToWorldbuilding);
};