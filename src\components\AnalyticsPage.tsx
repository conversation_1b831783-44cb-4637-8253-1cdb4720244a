import React, { useState } from 'react';
import { ArrowLeft, Target, TrendingUp, Calendar, Award, FileText } from 'lucide-react';
import { useAnalytics } from '../hooks/useAnalytics';
import { useImageBrightness } from '../hooks/useImageBrightness';
import { Book } from '../types';

const getAllSceneWordCounts = (book: Book) => {
  const sceneWordCounts: Array<{
    chapterTitle: string;
    sceneTitle: string;
    wordCount: number;
    chapterIndex: number;
    sceneIndex: number;
  }> = [];

  book.chapters.forEach((chapter, chapterIndex) => {
    chapter.scenes.forEach((scene, sceneIndex) => {
      sceneWordCounts.push({
        chapterTitle: chapter.title,
        sceneTitle: scene.title,
        wordCount: scene.wordCount,
        chapterIndex: chapterIndex + 1,
        sceneIndex: sceneIndex + 1
      });
    });
  });

  return sceneWordCounts;
};

interface AnalyticsPageProps {
  userId: string;
  book: Book | null;
  onBack: () => void;
  backgroundImage: string;
}

export const AnalyticsPage: React.FC<AnalyticsPageProps> = ({ 
  userId, 
  book,
  onBack,
  backgroundImage
}) => {
  const { goals, stats, loading, saveGoals } = useAnalytics(userId);
  const sceneWordCounts = book ? getAllSceneWordCounts(book) : [];
  const [showGoalInput, setShowGoalInput] = useState(false);
  const [newGoal, setNewGoal] = useState(goals.weekly);
  const isBackgroundDark = useImageBrightness(backgroundImage);
  const glassClass = isBackgroundDark ? 'bg-black/40' : 'bg-white/40';

  const getProgressPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  // Update newGoal when goals change
  React.useEffect(() => {
    setNewGoal(goals.weekly);
  }, [goals.weekly]);

  if (loading) {
    return (
      <div 
        className="min-h-screen relative"
        style={{
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed'
        }}
      >
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center text-white">
            <h2 className="text-2xl font-serif mb-4">Loading Analytics...</h2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="min-h-screen relative"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="absolute inset-0 bg-black/20" />
      
      <div className="relative z-10 p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <button
              onClick={onBack}
              className={`flex items-center px-4 py-2 ${glassClass} backdrop-blur-md rounded-xl border border-white/10 text-white/80 hover:text-white transition-colors mb-6`}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              <span className="font-sans">Back to Writing</span>
            </button>
            <h1 className="text-3xl font-serif font-bold text-white">Writing Analytics</h1>
          </div>

          {/* Weekly Progress */}
          <div className={`${glassClass} backdrop-blur-md rounded-2xl border border-white/10 p-6 mb-8`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white font-sans">Weekly Writing Progress</h3>
              <TrendingUp className="w-6 h-6 text-green-400" />
            </div>
            <div className="mb-6">
              <div className="flex justify-between text-sm text-white/60 mb-2 font-sans">
                <span>{stats.thisWeek.toLocaleString()} words written</span>
                <span>{goals.weekly.toLocaleString()} goal</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-4">
                <div
                  className="bg-green-400 h-4 rounded-full transition-all duration-300"
                  style={{ width: `${getProgressPercentage(stats.thisWeek, goals.weekly)}%` }}
                />
              </div>
            </div>
            <p className="text-3xl font-bold text-white font-sans mb-2">{Math.round(getProgressPercentage(stats.thisWeek, goals.weekly))}%</p>
            <p className="text-white/60 font-sans text-sm">
              {stats.thisWeek >= goals.weekly ? 'Goal achieved! 🎉' : `${(goals.weekly - stats.thisWeek).toLocaleString()} words to go`}
            </p>
            <button
              onClick={() => setShowGoalInput(!showGoalInput)}
              className="mt-4 px-4 py-2 bg-white/10 hover:bg-white/20 text-white/80 hover:text-white rounded-lg border border-white/20 transition-colors font-sans text-sm"
            >
              Change Goal
            </button>
            {showGoalInput && (
              <div className="mt-4 flex items-center space-x-3">
                <input
                  type="number"
                  value={newGoal}
                  onChange={(e) => setNewGoal(parseInt(e.target.value) || 0)}
                  className="px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent font-sans w-32"
                  placeholder="Goal"
                />
                <button
                  onClick={() => {
                    saveGoals({ weekly: newGoal });
                    setShowGoalInput(false);
                  }}
                  className="px-3 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg font-sans text-sm"
                >
                  Save
                </button>
                <button
                  onClick={() => setShowGoalInput(false)}
                  className="px-3 py-2 bg-white/10 hover:bg-white/20 text-white/80 rounded-lg font-sans text-sm"
                >
                  Cancel
                </button>
              </div>
            )}
          </div>



          {/* Scene Word Counts */}
          {book && (
            <div className={`${glassClass} backdrop-blur-md rounded-2xl border border-white/10 p-6`}>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-white font-sans">Scene Word Counts</h3>
                <FileText className="w-6 h-6 text-blue-400" />
              </div>
              <div className="mb-4">
                <p className="text-white/80 font-sans">Book: <span className="text-white font-semibold">{book.title}</span></p>
                <p className="text-white/60 font-sans text-sm">Total: {book.wordCount.toLocaleString()} words</p>
              </div>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {sceneWordCounts.map((scene, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div>
                      <p className="text-white font-sans text-sm">
                        Ch. {scene.chapterIndex} "{scene.chapterTitle}" - Scene {scene.sceneIndex}
                      </p>
                      <p className="text-white/60 font-sans text-xs">"{scene.sceneTitle}"</p>
                    </div>
                    <div className="text-white/80 font-sans font-medium">
                      {scene.wordCount.toLocaleString()} words
                    </div>
                  </div>
                ))}
                {sceneWordCounts.length === 0 && (
                  <div className="text-center text-white/60 font-sans py-8">
                    No scenes with content yet. Start writing to see word counts!
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};