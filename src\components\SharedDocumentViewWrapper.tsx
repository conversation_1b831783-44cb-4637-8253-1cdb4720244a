import React from 'react';
import { useParams } from 'react-router-dom';
import { SharedDocumentView } from './SharedDocumentView';

interface SharedDocumentViewWrapperProps {
  userId: string; // Firebase UID
  userName: string;
  userEmail: string;
  onClose: () => void;
  backgroundImage: string;
}

export const SharedDocumentViewWrapper: React.FC<SharedDocumentViewWrapperProps> = ({
  userId,
  userName,
  userEmail,
  onClose,
  backgroundImage
}) => {
  // Use React Router's useParams hook to get the sharedDocumentId
  const { sharedDocumentId } = useParams<{ sharedDocumentId: string }>();
  
  if (!sharedDocumentId) {
    return (
      <div 
        className="min-h-screen flex items-center justify-center relative"
        style={{
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed'
        }}
      >
        <div className="absolute inset-0 bg-black/30" />
        <div className="relative z-10 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 shadow-2xl">
          <h2 className="text-2xl font-serif font-semibold text-white mb-4">Error</h2>
          <p className="text-white/80 font-sans">Invalid shared document ID</p>
          <button
            onClick={onClose}
            className="mt-6 px-4 py-2 bg-white/20 hover:bg-white/30 border border-white/30 rounded-xl text-white font-sans transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <SharedDocumentView
      sharedDocumentId={sharedDocumentId}
      userId={userId}
      userName={userName}
      userEmail={userEmail}
      onClose={onClose}
      backgroundImage={backgroundImage}
    />
  );
};