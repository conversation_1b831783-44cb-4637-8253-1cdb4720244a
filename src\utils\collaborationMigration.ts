import { collection, getDocs, updateDoc, doc } from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import type { Book } from '../types';

/**
 * Migration utilities for adding collaboration features to existing books
 */

/**
 * Add collaboration fields to existing books
 */
export async function migrateExistingBooksToCollaborative(): Promise<{
  success: boolean;
  migratedCount: number;
  errors: string[];
}> {
  const results = {
    success: true,
    migratedCount: 0,
    errors: [] as string[]
  };

  try {
    console.log('🔄 Starting collaboration migration for existing books...');

    // Get all books
    const booksQuery = collection(db, COLLECTIONS.BOOKS);
    const booksSnapshot = await getDocs(booksQuery);

    console.log(`📚 Found ${booksSnapshot.docs.length} books to migrate`);

    for (const bookDoc of booksSnapshot.docs) {
      try {
        const bookData = bookDoc.data() as Book;
        
        // Check if book already has collaboration fields
        if (bookData.collaborators !== undefined && bookData.isCollaborative !== undefined) {
          console.log(`⏭️ Skipping ${bookData.title} - already has collaboration fields`);
          continue;
        }

        // Add collaboration fields
        const updateData: Partial<Book> = {
          collaborators: [],
          isCollaborative: false,
          updatedAt: new Date().toISOString()
        };

        await updateDoc(doc(db, COLLECTIONS.BOOKS, bookDoc.id), updateData);
        
        results.migratedCount++;
        console.log(`✅ Migrated: ${bookData.title}`);

      } catch (error) {
        const errorMsg = `Failed to migrate book ${bookDoc.id}: ${error}`;
        results.errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    }

    console.log(`🎉 Migration complete: ${results.migratedCount} books migrated, ${results.errors.length} errors`);

    if (results.errors.length > 0) {
      results.success = false;
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    results.success = false;
    results.errors.push(`Migration failed: ${error}`);
  }

  return results;
}

/**
 * Add edit tracking fields to existing scenes
 */
export async function migrateScenesForEditTracking(): Promise<{
  success: boolean;
  migratedCount: number;
  errors: string[];
}> {
  const results = {
    success: true,
    migratedCount: 0,
    errors: [] as string[]
  };

  try {
    console.log('🔄 Starting scene edit tracking migration...');

    // Get all books first
    const booksQuery = collection(db, COLLECTIONS.BOOKS);
    const booksSnapshot = await getDocs(booksQuery);

    for (const bookDoc of booksSnapshot.docs) {
      try {
        const bookData = bookDoc.data() as Book;
        
        // Process each chapter and scene
        for (const chapter of bookData.chapters || []) {
          for (const scene of chapter.scenes || []) {
            // Check if scene already has edit tracking fields
            if (scene.editChanges !== undefined) {
              continue;
            }

            // Add edit tracking fields to scene
            const updatedScene = {
              ...scene,
              editChanges: [],
              lastEditedBy: bookData.ownerId,
              lastEditedAt: scene.updatedAt || new Date().toISOString()
            };

            // Update the scene in the chapter
            const updatedChapter = {
              ...chapter,
              scenes: chapter.scenes.map(s => s.id === scene.id ? updatedScene : s)
            };

            // Update the book with the modified chapter
            const updatedBook = {
              ...bookData,
              chapters: bookData.chapters.map(c => c.id === chapter.id ? updatedChapter : c),
              updatedAt: new Date().toISOString()
            };

            await updateDoc(doc(db, COLLECTIONS.BOOKS, bookDoc.id), updatedBook);
            results.migratedCount++;
          }
        }

        console.log(`✅ Migrated scenes for: ${bookData.title}`);

      } catch (error) {
        const errorMsg = `Failed to migrate scenes for book ${bookDoc.id}: ${error}`;
        results.errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    }

    console.log(`🎉 Scene migration complete: ${results.migratedCount} scenes migrated, ${results.errors.length} errors`);

    if (results.errors.length > 0) {
      results.success = false;
    }

  } catch (error) {
    console.error('❌ Scene migration failed:', error);
    results.success = false;
    results.errors.push(`Scene migration failed: ${error}`);
  }

  return results;
}

/**
 * Run all migrations
 */
export async function runAllCollaborationMigrations(): Promise<{
  success: boolean;
  results: {
    books: { success: boolean; migratedCount: number; errors: string[] };
    scenes: { success: boolean; migratedCount: number; errors: string[] };
  };
}> {
  console.log('🚀 Starting all collaboration migrations...');

  const bookMigration = await migrateExistingBooksToCollaborative();
  const sceneMigration = await migrateScenesForEditTracking();

  const overallSuccess = bookMigration.success && sceneMigration.success;

  console.log('📊 Migration Summary:');
  console.log(`   Books: ${bookMigration.migratedCount} migrated, ${bookMigration.errors.length} errors`);
  console.log(`   Scenes: ${sceneMigration.migratedCount} migrated, ${sceneMigration.errors.length} errors`);
  console.log(`   Overall: ${overallSuccess ? 'SUCCESS' : 'PARTIAL/FAILED'}`);

  return {
    success: overallSuccess,
    results: {
      books: bookMigration,
      scenes: sceneMigration
    }
  };
}

/**
 * Check if migrations are needed
 */
export async function checkMigrationStatus(): Promise<{
  needsBookMigration: boolean;
  needsSceneMigration: boolean;
  totalBooks: number;
  booksWithCollaboration: number;
}> {
  try {
    const booksQuery = collection(db, COLLECTIONS.BOOKS);
    const booksSnapshot = await getDocs(booksQuery);
    
    let booksWithCollaboration = 0;
    
    for (const bookDoc of booksSnapshot.docs) {
      const bookData = bookDoc.data() as Book;
      if (bookData.collaborators !== undefined && bookData.isCollaborative !== undefined) {
        booksWithCollaboration++;
      }
    }

    const totalBooks = booksSnapshot.docs.length;
    const needsBookMigration = booksWithCollaboration < totalBooks;
    
    // For scenes, we'd need to check each scene individually
    // For simplicity, we'll assume scene migration is needed if book migration is needed
    const needsSceneMigration = needsBookMigration;

    return {
      needsBookMigration,
      needsSceneMigration,
      totalBooks,
      booksWithCollaboration
    };
  } catch (error) {
    console.error('Error checking migration status:', error);
    return {
      needsBookMigration: true,
      needsSceneMigration: true,
      totalBooks: 0,
      booksWithCollaboration: 0
    };
  }
}

// Make migration functions available globally for manual execution
declare global {
  interface Window {
    collaborationMigration: {
      migrateBooks: typeof migrateExistingBooksToCollaborative;
      migrateScenes: typeof migrateScenesForEditTracking;
      runAll: typeof runAllCollaborationMigrations;
      checkStatus: typeof checkMigrationStatus;
    };
  }
}

// Expose migration utilities globally
if (typeof window !== 'undefined') {
  window.collaborationMigration = {
    migrateBooks: migrateExistingBooksToCollaborative,
    migrateScenes: migrateScenesForEditTracking,
    runAll: runAllCollaborationMigrations,
    checkStatus: checkMigrationStatus
  };

  console.log('🔧 Collaboration migration utilities available:');
  console.log('- collaborationMigration.checkStatus() - Check if migration is needed');
  console.log('- collaborationMigration.runAll() - Run all migrations');
  console.log('- collaborationMigration.migrateBooks() - Migrate books only');
  console.log('- collaborationMigration.migrateScenes() - Migrate scenes only');
}
