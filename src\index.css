@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  .font-serif {
    font-family: 'Hedvig Letters Serif', serif;
  }
  
  .font-sans {
    font-family: 'Be Vietnam Pro', sans-serif;
    letter-spacing: -0.03em;
  }
  
  /* TipTap Editor Styles */
  .ProseMirror {
    outline: none;
    color: #1f2937;
    line-height: 1.8;
    font-size: 18px;
  }
  
  .ProseMirror p {
    margin: 1em 0;
    line-height: 1.8;
  }
  
  .ProseMirror p:first-child {
    margin-top: 0;
  }
  
  .ProseMirror p:last-child {
    margin-bottom: 0;
  }
  
  .ProseMirror strong {
    font-weight: 600;
  }
  
  .ProseMirror em {
    font-style: italic;
  }
  
  .ProseMirror ul, .ProseMirror ol {
    padding-left: 1.5rem;
    margin: 1em 0;
  }
  
  .ProseMirror li {
    margin: 0.5em 0;
  }
  
  .ProseMirror blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 1.5em 0;
    font-style: italic;
    color: #6b7280;
  }
  
  .ProseMirror pre {
    background: #f3f4f6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1em 0;
    overflow-x: auto;
  }
  
  .ProseMirror code {
    background: #f3f4f6;
    padding: 0.2em 0.4em;
    border-radius: 0.25rem;
    font-size: 0.9em;
  }
  
  .ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
  }
  
  .ProseMirror img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1em 0;
  }
  
  .ProseMirror table {
    border-collapse: collapse;
    margin: 1em 0;
    width: 100%;
  }
  
  .ProseMirror th, .ProseMirror td {
    border: 1px solid #e5e7eb;
    padding: 0.5rem;
    text-align: left;
  }
  
  .ProseMirror th {
    background: #f9fafb;
    font-weight: 600;
  }
  
  .ProseMirror hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 2em 0;
  }
}
