import React, { useState, useEffect } from 'react';
import { X, Plus, Users, Crown, Edit, Eye, Trash2, UserCheck } from 'lucide-react';
import { CollaborativeEditingService } from '../services/collaborativeEditingService';
import type { Book, BookCollaborator, UserRole } from '../types';

interface BookCollaborationModalProps {
  isOpen: boolean;
  onClose: () => void;
  book: Book;
  currentUserId: string;
  onBookUpdate: (updatedBook: Book) => void;
  backgroundImage: string;
}

export const BookCollaborationModal: React.FC<BookCollaborationModalProps> = ({
  isOpen,
  onClose,
  book,
  currentUserId,
  onBookUpdate,
  backgroundImage
}) => {
  const [newCollaboratorEmail, setNewCollaboratorEmail] = useState('');
  const [newCollaboratorRole, setNewCollaboratorRole] = useState<UserRole>('editor');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const isOwner = book.ownerId === currentUserId;
  const userRole = CollaborativeEditingService.getUserRoleInBook(book, currentUserId);

  useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null);
        setError(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  const handleAddCollaborator = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCollaboratorEmail.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await CollaborativeEditingService.addCollaborator(
        book.id,
        newCollaboratorEmail.trim(),
        newCollaboratorRole,
        currentUserId,
        'Current User' // In a real app, you'd get this from user context
      );

      if (result.success) {
        setSuccess(`Successfully added ${newCollaboratorEmail} as ${newCollaboratorRole}`);
        setNewCollaboratorEmail('');
        setNewCollaboratorRole('editor');
        
        // Refresh book data (in a real app, you'd use real-time listeners)
        // For now, we'll just close and let the parent refresh
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setError(result.error || 'Failed to add collaborator');
      }
    } catch (err) {
      setError('Failed to add collaborator');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveCollaborator = async (collaboratorUserId: string) => {
    if (!confirm('Are you sure you want to remove this collaborator?')) return;

    setIsLoading(true);
    try {
      const result = await CollaborativeEditingService.removeCollaborator(book.id, collaboratorUserId);
      
      if (result.success) {
        setSuccess('Collaborator removed successfully');
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setError(result.error || 'Failed to remove collaborator');
      }
    } catch (err) {
      setError('Failed to remove collaborator');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateRole = async (collaboratorUserId: string, newRole: UserRole) => {
    setIsLoading(true);
    try {
      const result = await CollaborativeEditingService.updateCollaboratorRole(
        book.id,
        collaboratorUserId,
        newRole
      );
      
      if (result.success) {
        setSuccess('Role updated successfully');
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setError(result.error || 'Failed to update role');
      }
    } catch (err) {
      setError('Failed to update role');
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'author':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'editor':
        return <Edit className="w-4 h-4 text-blue-500" />;
      case 'viewer':
        return <Eye className="w-4 h-4 text-gray-500" />;
      default:
        return <Users className="w-4 h-4 text-gray-500" />;
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'author':
        return 'text-yellow-600 bg-yellow-100';
      case 'editor':
        return 'text-blue-600 bg-blue-100';
      case 'viewer':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      <div className="relative z-10 w-full max-w-2xl bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-serif font-semibold text-white">Book Collaboration</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-lg">
            <p className="text-red-700 font-sans text-sm">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-100 border border-green-300 rounded-lg">
            <p className="text-green-700 font-sans text-sm">{success}</p>
          </div>
        )}

        <div className="space-y-6 overflow-y-auto max-h-96">
          {/* Book Info */}
          <div className="p-4 bg-white/5 border border-white/10 rounded-xl">
            <h3 className="text-lg font-serif text-white mb-2">{book.title}</h3>
            <p className="text-white/60 font-sans text-sm">
              by {book.author} • {book.collaborators?.length || 0} collaborator{(book.collaborators?.length || 0) !== 1 ? 's' : ''}
            </p>
          </div>

          {/* Add Collaborator (Owner only) */}
          {isOwner && (
            <div className="p-4 bg-white/5 border border-white/10 rounded-xl">
              <h3 className="text-lg font-serif text-white mb-3">Add Collaborator</h3>
              <form onSubmit={handleAddCollaborator} className="space-y-3">
                <div>
                  <label className="block text-white/80 font-sans text-sm mb-1">Email</label>
                  <input
                    type="email"
                    value={newCollaboratorEmail}
                    onChange={(e) => setNewCollaboratorEmail(e.target.value)}
                    className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 font-sans"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div>
                  <label className="block text-white/80 font-sans text-sm mb-1">Role</label>
                  <select
                    value={newCollaboratorRole}
                    onChange={(e) => setNewCollaboratorRole(e.target.value as UserRole)}
                    className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white font-sans"
                  >
                    <option value="editor">Editor - Can make tracked changes</option>
                    <option value="viewer">Viewer - Read-only access</option>
                  </select>
                </div>
                <button
                  type="submit"
                  disabled={isLoading || !newCollaboratorEmail.trim()}
                  className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-sans transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Collaborator
                </button>
              </form>
            </div>
          )}

          {/* Current Collaborators */}
          <div className="p-4 bg-white/5 border border-white/10 rounded-xl">
            <h3 className="text-lg font-serif text-white mb-3">Current Collaborators</h3>
            
            {/* Owner */}
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-white/10 rounded-lg">
                <div className="flex items-center">
                  <Crown className="w-5 h-5 text-yellow-500 mr-3" />
                  <div>
                    <div className="text-white font-sans font-medium">{book.author}</div>
                    <div className="text-white/60 font-sans text-sm">Owner</div>
                  </div>
                </div>
                <span className="px-2 py-1 bg-yellow-100 text-yellow-600 rounded-full text-xs font-sans">
                  Author
                </span>
              </div>

              {/* Collaborators */}
              {book.collaborators?.filter(c => c.isActive).map((collaborator) => (
                <div key={collaborator.userId} className="flex items-center justify-between p-3 bg-white/10 rounded-lg">
                  <div className="flex items-center">
                    {getRoleIcon(collaborator.role)}
                    <div className="ml-3">
                      <div className="text-white font-sans font-medium">{collaborator.userName}</div>
                      <div className="text-white/60 font-sans text-sm">{collaborator.userEmail}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-sans ${getRoleColor(collaborator.role)}`}>
                      {collaborator.role}
                    </span>
                    
                    {isOwner && (
                      <div className="flex space-x-1">
                        {collaborator.role !== 'editor' && (
                          <button
                            onClick={() => handleUpdateRole(collaborator.userId, 'editor')}
                            className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                            title="Make Editor"
                          >
                            <Edit className="w-3 h-3" />
                          </button>
                        )}
                        {collaborator.role !== 'viewer' && (
                          <button
                            onClick={() => handleUpdateRole(collaborator.userId, 'viewer')}
                            className="p-1 text-gray-600 hover:bg-gray-100 rounded"
                            title="Make Viewer"
                          >
                            <Eye className="w-3 h-3" />
                          </button>
                        )}
                        <button
                          onClick={() => handleRemoveCollaborator(collaborator.userId)}
                          className="p-1 text-red-600 hover:bg-red-100 rounded"
                          title="Remove Collaborator"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {(!book.collaborators || book.collaborators.filter(c => c.isActive).length === 0) && (
                <div className="text-center py-4 text-white/60 font-sans">
                  No collaborators yet
                </div>
              )}
            </div>
          </div>

          {/* Collaboration Info */}
          <div className="p-4 bg-white/5 border border-white/10 rounded-xl">
            <h3 className="text-lg font-serif text-white mb-2">How Collaboration Works</h3>
            <div className="space-y-2 text-white/80 font-sans text-sm">
              <div className="flex items-start">
                <Crown className="w-4 h-4 text-yellow-500 mr-2 mt-0.5" />
                <span><strong>Authors</strong> can accept/reject changes and manage collaborators</span>
              </div>
              <div className="flex items-start">
                <Edit className="w-4 h-4 text-blue-500 mr-2 mt-0.5" />
                <span><strong>Editors</strong> can make tracked changes that appear highlighted</span>
              </div>
              <div className="flex items-start">
                <Eye className="w-4 h-4 text-gray-500 mr-2 mt-0.5" />
                <span><strong>Viewers</strong> can read the document but cannot make changes</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
