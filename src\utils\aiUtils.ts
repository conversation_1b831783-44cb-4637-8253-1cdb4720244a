export const generateCharacterName = async (description: string): Promise<string> => {
  try {
    const response = await fetch('/api/generate-character', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ description }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate character name');
    }

    const data = await response.json();
    return data.name;
  } catch (error) {
    console.error('Error generating character name:', error);
    throw new Error('Failed to generate character name');
  }
};

export const generateWritingPrompt = async (currentContent: string): Promise<string> => {
  try {
    const response = await fetch('/api/generate-prompt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ content: currentContent }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate writing prompt');
    }

    const data = await response.json();
    return data.prompt;
  } catch (error) {
    console.error('Error generating writing prompt:', error);
    throw new Error('Failed to generate writing prompt');
  }
};