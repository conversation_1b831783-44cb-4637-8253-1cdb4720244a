import { auth, db } from '../lib/firebase'
import { collection, addDoc, getDocs, deleteDoc, doc } from 'firebase/firestore'

export const testFirebaseConnection = async () => {
  try {
    console.log('Testing Firebase connection...')
    
    // Test Firestore connection
    const testCollection = collection(db, 'test')
    const testDoc = await addDoc(testCollection, {
      message: 'Firebase connection test',
      timestamp: new Date()
    })
    
    console.log('✅ Firestore write test passed')
    
    // Test reading
    const snapshot = await getDocs(testCollection)
    console.log('✅ Firestore read test passed, documents:', snapshot.size)
    
    // Clean up test document
    await deleteDoc(doc(db, 'test', testDoc.id))
    console.log('✅ Firestore delete test passed')
    
    // Test Auth
    console.log('Auth current user:', auth.currentUser)
    console.log('✅ Firebase Auth initialized')
    
    return true
  } catch (error) {
    console.error('❌ Firebase connection test failed:', error)
    return false
  }
}

// Uncomment to run test on app load
// testFirebaseConnection()
