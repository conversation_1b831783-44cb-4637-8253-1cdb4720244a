import React from 'react';
import { Bell, CheckCircle } from 'lucide-react';

interface ReturnedDocumentNotificationProps {
  documentId: string;
  documentTitle: string;
  editorName: string;
  onReview: (documentId: string) => void;
  onDismiss: (documentId: string) => void;
}

export const ReturnedDocumentNotification: React.FC<ReturnedDocumentNotificationProps> = ({
  documentId,
  documentTitle,
  editorName,
  onReview,
  onDismiss
}) => {
  return (
    <div className="fixed bottom-4 right-4 w-80 bg-white rounded-lg shadow-lg border border-blue-200 overflow-hidden z-50">
      <div className="bg-blue-600 px-4 py-2 flex items-center">
        <Bell className="w-5 h-5 text-white mr-2" />
        <h3 className="text-white font-medium">Document Returned</h3>
      </div>
      
      <div className="p-4">
        <p className="text-gray-800 font-medium mb-1">{documentTitle}</p>
        <p className="text-gray-600 text-sm mb-3">
          {editorN<PERSON>} has completed editing this document and returned it with changes.
        </p>
        
        <div className="flex justify-end space-x-2">
          <button 
            onClick={() => onDismiss(documentId)}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
          >
            Dismiss
          </button>
          <button 
            onClick={() => onReview(documentId)}
            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded"
          >
            Review Changes
          </button>
        </div>
      </div>
    </div>
  );
};