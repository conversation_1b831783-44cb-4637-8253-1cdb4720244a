import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';

/**
 * Debug utilities for document sharing
 */

/**
 * Debug function to check shared documents in Firestore
 */
export async function debugSharedDocuments(userId: string) {
  console.log('🔍 Debugging shared documents for user:', userId);
  
  try {
    // Check documents shared BY the user
    console.log('\n📤 Documents shared BY me:');
    const sharedByMeQuery = query(
      collection(db, COLLECTIONS.SHARED_DOCUMENTS),
      where('senderId', '==', userId)
    );
    const sharedByMeSnapshot = await getDocs(sharedByMeQuery);
    
    if (sharedByMeSnapshot.empty) {
      console.log('   No documents shared by me found');
    } else {
      sharedByMeSnapshot.docs.forEach((doc, index) => {
        const data = doc.data();
        console.log(`   ${index + 1}. ${data.bookTitle} → ${data.recipientEmail} (${data.status})`);
        console.log(`      ID: ${doc.id}`);
        console.log(`      Created: ${data.createdAt?.toDate()}`);
        console.log(`      Permission: ${data.permission}`);
      });
    }
    
    // Check documents shared WITH the user
    console.log('\n📥 Documents shared WITH me:');
    const sharedWithMeQuery = query(
      collection(db, COLLECTIONS.SHARED_DOCUMENTS),
      where('recipientId', '==', userId)
    );
    const sharedWithMeSnapshot = await getDocs(sharedWithMeQuery);
    
    if (sharedWithMeSnapshot.empty) {
      console.log('   No documents shared with me found');
    } else {
      sharedWithMeSnapshot.docs.forEach((doc, index) => {
        const data = doc.data();
        console.log(`   ${index + 1}. ${data.bookTitle} from ${data.senderName} (${data.status})`);
        console.log(`      ID: ${doc.id}`);
        console.log(`      Created: ${data.createdAt?.toDate()}`);
        console.log(`      Permission: ${data.permission}`);
      });
    }
    
    // Check all shared documents (for debugging)
    console.log('\n📋 ALL shared documents:');
    const allSharedQuery = collection(db, COLLECTIONS.SHARED_DOCUMENTS);
    const allSharedSnapshot = await getDocs(allSharedQuery);
    
    if (allSharedSnapshot.empty) {
      console.log('   No shared documents found in database');
    } else {
      allSharedSnapshot.docs.forEach((doc, index) => {
        const data = doc.data();
        console.log(`   ${index + 1}. ${data.bookTitle}`);
        console.log(`      From: ${data.senderName} (${data.senderId})`);
        console.log(`      To: ${data.recipientEmail} (${data.recipientId})`);
        console.log(`      Status: ${data.status}`);
        console.log(`      ID: ${doc.id}`);
        console.log('      ---');
      });
    }
    
  } catch (error) {
    console.error('❌ Error debugging shared documents:', error);
  }
}

/**
 * Debug function to check user lookup table
 */
export async function debugUserLookup() {
  console.log('🔍 Debugging user lookup table:');
  
  try {
    const userLookupQuery = collection(db, COLLECTIONS.USER_LOOKUP || 'userLookup');
    const userLookupSnapshot = await getDocs(userLookupQuery);
    
    if (userLookupSnapshot.empty) {
      console.log('   No users found in lookup table');
    } else {
      console.log(`   Found ${userLookupSnapshot.docs.length} users:`);
      userLookupSnapshot.docs.forEach((doc, index) => {
        const data = doc.data();
        console.log(`   ${index + 1}. ${data.email}`);
        console.log(`      UID: ${data.uid}`);
        console.log(`      Name: ${data.displayName || 'N/A'}`);
        console.log(`      Active: ${data.isActive}`);
        console.log(`      Last seen: ${data.lastSeen?.toDate()}`);
        console.log('      ---');
      });
    }
    
  } catch (error) {
    console.error('❌ Error debugging user lookup:', error);
  }
}

/**
 * Debug function to check notifications
 */
export async function debugNotifications(userId: string) {
  console.log('🔍 Debugging notifications for user:', userId);
  
  try {
    const notificationsQuery = query(
      collection(db, 'notifications'),
      where('userId', '==', userId)
    );
    const notificationsSnapshot = await getDocs(notificationsQuery);
    
    if (notificationsSnapshot.empty) {
      console.log('   No notifications found');
    } else {
      console.log(`   Found ${notificationsSnapshot.docs.length} notifications:`);
      notificationsSnapshot.docs.forEach((doc, index) => {
        const data = doc.data();
        console.log(`   ${index + 1}. ${data.title}`);
        console.log(`      Type: ${data.type}`);
        console.log(`      Message: ${data.message}`);
        console.log(`      Read: ${data.isRead}`);
        console.log(`      Created: ${data.createdAt?.toDate()}`);
        console.log('      ---');
      });
    }
    
  } catch (error) {
    console.error('❌ Error debugging notifications:', error);
  }
}

/**
 * Debug function to check share activity
 */
export async function debugShareActivity(sharedDocumentId?: string) {
  console.log('🔍 Debugging share activity:', sharedDocumentId || 'all');
  
  try {
    let activityQuery;
    if (sharedDocumentId) {
      activityQuery = query(
        collection(db, COLLECTIONS.SHARE_ACTIVITY),
        where('sharedDocumentId', '==', sharedDocumentId)
      );
    } else {
      activityQuery = collection(db, COLLECTIONS.SHARE_ACTIVITY);
    }
    
    const activitySnapshot = await getDocs(activityQuery);
    
    if (activitySnapshot.empty) {
      console.log('   No activity found');
    } else {
      console.log(`   Found ${activitySnapshot.docs.length} activities:`);
      activitySnapshot.docs.forEach((doc, index) => {
        const data = doc.data();
        console.log(`   ${index + 1}. ${data.action} by ${data.userName}`);
        console.log(`      Document: ${data.sharedDocumentId}`);
        console.log(`      Details: ${data.details || 'N/A'}`);
        console.log(`      Time: ${data.timestamp?.toDate()}`);
        console.log('      ---');
      });
    }
    
  } catch (error) {
    console.error('❌ Error debugging share activity:', error);
  }
}

/**
 * Run all debug functions
 */
export async function debugAll(userId: string) {
  console.log('🚀 Running complete sharing debug for user:', userId);
  console.log('='.repeat(50));
  
  await debugUserLookup();
  console.log('\n' + '='.repeat(50));
  
  await debugSharedDocuments(userId);
  console.log('\n' + '='.repeat(50));
  
  await debugNotifications(userId);
  console.log('\n' + '='.repeat(50));
  
  await debugShareActivity();
  console.log('\n' + '='.repeat(50));
  
  console.log('🎉 Debug complete!');
}

// Make debug functions available globally
declare global {
  interface Window {
    debugSharing: {
      debugSharedDocuments: typeof debugSharedDocuments;
      debugUserLookup: typeof debugUserLookup;
      debugNotifications: typeof debugNotifications;
      debugShareActivity: typeof debugShareActivity;
      debugAll: typeof debugAll;
    };
  }
}

// Expose debug utilities globally
if (typeof window !== 'undefined') {
  window.debugSharing = {
    debugSharedDocuments,
    debugUserLookup,
    debugNotifications,
    debugShareActivity,
    debugAll
  };

  console.log('🔧 Sharing debug utilities available:');
  console.log('- debugSharing.debugAll(userId) - Run all debug functions');
  console.log('- debugSharing.debugSharedDocuments(userId) - Check shared documents');
  console.log('- debugSharing.debugUserLookup() - Check user lookup table');
  console.log('- debugSharing.debugNotifications(userId) - Check notifications');
  console.log('- debugSharing.debugShareActivity(sharedDocId?) - Check activity logs');
}
