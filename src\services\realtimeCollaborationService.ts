import { 
  collection, 
  doc, 
  onSnapshot, 
  updateDoc,
  addDoc,
  query,
  where,
  orderBy,
  Timestamp,
  Unsubscribe
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import type { Comment } from '../types/sharing';

/**
 * Service for real-time collaboration features
 */
export class RealtimeCollaborationService {
  private static activeListeners: Map<string, Unsubscribe> = new Map();

  /**
   * Subscribe to real-time document changes
   */
  static subscribeToDocumentChanges(
    sharedDocumentId: string,
    onDocumentChange: (documentData: any) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, 'bookData', 'book');
    
    const unsubscribe = onSnapshot(
      docRef,
      (docSnapshot) => {
        if (docSnapshot.exists()) {
          onDocumentChange(docSnapshot.data());
        }
      },
      (error) => {
        console.error('Error listening to document changes:', error);
        if (onError) onError(error);
      }
    );

    // Store the unsubscribe function
    this.activeListeners.set(`doc-${sharedDocumentId}`, unsubscribe);
    
    return unsubscribe;
  }

  /**
   * Subscribe to real-time comments
   */
  static subscribeToComments(
    sharedDocumentId: string,
    onCommentsChange: (comments: Comment[]) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    const commentsRef = collection(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, COLLECTIONS.COMMENTS);
    const q = query(commentsRef, orderBy('createdAt', 'asc'));
    
    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        const comments: Comment[] = [];
        querySnapshot.forEach((doc) => {
          comments.push({ id: doc.id, ...doc.data() } as Comment);
        });
        onCommentsChange(comments);
      },
      (error) => {
        console.error('Error listening to comments:', error);
        if (onError) onError(error);
      }
    );

    // Store the unsubscribe function
    this.activeListeners.set(`comments-${sharedDocumentId}`, unsubscribe);
    
    return unsubscribe;
  }

  /**
   * Subscribe to active collaborators (users currently viewing/editing)
   */
  static subscribeToActiveCollaborators(
    sharedDocumentId: string,
    onCollaboratorsChange: (collaborators: Array<{
      userId: string;
      userName: string;
      lastSeen: Timestamp;
      isActive: boolean;
    }>) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    // This would require a separate collection to track active users
    // For now, we'll use the share activity to approximate active users
    const activityRef = collection(db, COLLECTIONS.SHARE_ACTIVITY);
    const q = query(
      activityRef,
      where('sharedDocumentId', '==', sharedDocumentId),
      orderBy('timestamp', 'desc')
    );
    
    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        const recentActivity = new Map();
        const fiveMinutesAgo = Timestamp.fromDate(new Date(Date.now() - 5 * 60 * 1000));
        
        querySnapshot.forEach((doc) => {
          const activity = doc.data();
          if (activity.timestamp.toMillis() > fiveMinutesAgo.toMillis()) {
            recentActivity.set(activity.userId, {
              userId: activity.userId,
              userName: activity.userName,
              lastSeen: activity.timestamp,
              isActive: true
            });
          }
        });
        
        onCollaboratorsChange(Array.from(recentActivity.values()));
      },
      (error) => {
        console.error('Error listening to collaborators:', error);
        if (onError) onError(error);
      }
    );

    // Store the unsubscribe function
    this.activeListeners.set(`collaborators-${sharedDocumentId}`, unsubscribe);
    
    return unsubscribe;
  }

  /**
   * Update document content with conflict resolution
   */
  static async updateDocumentContent(
    sharedDocumentId: string,
    updatedContent: any,
    userId: string,
    lastKnownVersion?: number
  ): Promise<{
    success: boolean;
    conflict?: boolean;
    error?: string;
  }> {
    try {
      const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, 'bookData', 'book');
      
      // Add version tracking for conflict resolution
      const updateData = {
        ...updatedContent,
        lastEditedBy: userId,
        lastEditedAt: Timestamp.now(),
        version: (lastKnownVersion || 0) + 1
      };

      await updateDoc(docRef, updateData);

      return { success: true };
    } catch (error) {
      console.error('Error updating document content:', error);
      return {
        success: false,
        error: 'Failed to update document content'
      };
    }
  }

  /**
   * Add a real-time comment
   */
  static async addRealtimeComment(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    userEmail: string,
    content: string,
    position?: {
      chapterId: string;
      sceneId: string;
      textPosition: number;
    }
  ): Promise<{
    success: boolean;
    commentId?: string;
    error?: string;
  }> {
    try {
      const commentsRef = collection(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, COLLECTIONS.COMMENTS);
      
      const commentData = {
        sharedDocumentId,
        userId,
        userName,
        userEmail,
        content,
        position: position || null,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        editedAt: null,
        isResolved: false,
        resolvedBy: null,
        resolvedAt: null
      };

      const commentRef = await addDoc(commentsRef, commentData);

      return {
        success: true,
        commentId: commentRef.id
      };
    } catch (error) {
      console.error('Error adding realtime comment:', error);
      return {
        success: false,
        error: 'Failed to add comment'
      };
    }
  }

  /**
   * Resolve a comment
   */
  static async resolveComment(
    sharedDocumentId: string,
    commentId: string,
    userId: string,
    userName: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const commentRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, COLLECTIONS.COMMENTS, commentId);
      
      await updateDoc(commentRef, {
        isResolved: true,
        resolvedBy: userName,
        resolvedAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });

      return { success: true };
    } catch (error) {
      console.error('Error resolving comment:', error);
      return {
        success: false,
        error: 'Failed to resolve comment'
      };
    }
  }

  /**
   * Update user presence (heartbeat)
   */
  static async updateUserPresence(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    action: 'viewing' | 'editing' | 'idle' = 'viewing'
  ): Promise<void> {
    try {
      // This would typically use a separate presence collection
      // For now, we'll log it as activity
      const activityRef = collection(db, COLLECTIONS.SHARE_ACTIVITY);
      
      await addDoc(activityRef, {
        sharedDocumentId,
        userId,
        userName,
        action,
        details: `User is ${action}`,
        timestamp: Timestamp.now(),
        ipAddress: null,
        userAgent: navigator.userAgent
      });
    } catch (error) {
      console.error('Error updating user presence:', error);
    }
  }

  /**
   * Start presence heartbeat
   */
  static startPresenceHeartbeat(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    intervalSeconds: number = 30
  ): () => void {
    const intervalId = setInterval(() => {
      this.updateUserPresence(sharedDocumentId, userId, userName, 'viewing');
    }, intervalSeconds * 1000);

    // Initial presence update
    this.updateUserPresence(sharedDocumentId, userId, userName, 'viewing');

    // Return cleanup function
    return () => {
      clearInterval(intervalId);
    };
  }

  /**
   * Cleanup all listeners for a shared document
   */
  static cleanupListeners(sharedDocumentId: string): void {
    const listenersToCleanup = [
      `doc-${sharedDocumentId}`,
      `comments-${sharedDocumentId}`,
      `collaborators-${sharedDocumentId}`
    ];

    listenersToCleanup.forEach(key => {
      const unsubscribe = this.activeListeners.get(key);
      if (unsubscribe) {
        unsubscribe();
        this.activeListeners.delete(key);
      }
    });
  }

  /**
   * Cleanup all active listeners
   */
  static cleanupAllListeners(): void {
    this.activeListeners.forEach((unsubscribe) => {
      unsubscribe();
    });
    this.activeListeners.clear();
  }

  /**
   * Get conflict resolution suggestions
   */
  static getConflictResolutionSuggestions(
    localChanges: any,
    remoteChanges: any
  ): {
    canAutoResolve: boolean;
    suggestions: string[];
    mergedContent?: any;
  } {
    // Simple conflict resolution logic
    // In a real implementation, you'd have more sophisticated merging
    
    const suggestions: string[] = [];
    let canAutoResolve = true;

    // Check for conflicting changes
    if (localChanges.lastEditedAt && remoteChanges.lastEditedAt) {
      if (localChanges.lastEditedAt.toMillis() < remoteChanges.lastEditedAt.toMillis()) {
        suggestions.push('Remote changes are newer. Consider accepting remote changes.');
      } else {
        suggestions.push('Your changes are newer. Consider keeping your changes.');
      }
    }

    // Check for content conflicts
    if (localChanges.content !== remoteChanges.content) {
      canAutoResolve = false;
      suggestions.push('Content conflicts detected. Manual review required.');
    }

    return {
      canAutoResolve,
      suggestions,
      mergedContent: canAutoResolve ? remoteChanges : undefined
    };
  }
}
