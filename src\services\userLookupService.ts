import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  query, 
  where, 
  getDocs, 
  updateDoc,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import { UserLookup } from '../types/sharing';

/**
 * Service for managing user lookups and email-to-UID mapping
 */
export class UserLookupService {
  
  /**
   * Register or update a user in the lookup table
   */
  static async registerUser(uid: string, email: string, displayName: string | null = null): Promise<void> {
    try {
      const userLookupRef = doc(db, COLLECTIONS.USER_LOOKUP || 'userLookup', email.toLowerCase());
      
      const userLookup: Omit<UserLookup, 'email'> = {
        uid,
        displayName,
        lastSeen: Timestamp.now(),
        isActive: true
      };

      await setDoc(userLookupRef, {
        email: email.toLowerCase(),
        ...userLookup
      }, { merge: true });
      
    } catch (error) {
      console.error('Error registering user lookup:', error);
      throw new Error('Failed to register user lookup');
    }
  }

  /**
   * Find user UID by email address
   */
  static async findUserByEmail(email: string): Promise<UserLookup | null> {
    try {
      const userLookupRef = doc(db, COLLECTIONS.USER_LOOKUP || 'userLookup', email.toLowerCase());
      const userLookupSnap = await getDoc(userLookupRef);
      
      if (!userLookupSnap.exists()) {
        return null;
      }
      
      const userData = userLookupSnap.data() as UserLookup;
      
      // Update last seen
      await updateDoc(userLookupRef, {
        lastSeen: Timestamp.now()
      });
      
      return userData;
    } catch (error) {
      console.error('Error finding user by email:', error);
      return null;
    }
  }

  /**
   * Find user by UID
   */
  static async findUserByUid(uid: string): Promise<UserLookup | null> {
    try {
      const q = query(
        collection(db, COLLECTIONS.USER_LOOKUP || 'userLookup'),
        where('uid', '==', uid),
        where('isActive', '==', true)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }
      
      const userData = querySnapshot.docs[0].data() as UserLookup;
      return userData;
    } catch (error) {
      console.error('Error finding user by UID:', error);
      return null;
    }
  }

  /**
   * Check if user exists in Firebase Auth (fallback method)
   */
  static async checkFirebaseAuthUser(email: string): Promise<{
    exists: boolean;
    uid?: string;
    displayName?: string;
  }> {
    try {
      // This would require Firebase Admin SDK in a real implementation
      // For now, we'll simulate this check
      console.log(`Checking Firebase Auth for user: ${email}`);

      // In a real implementation, you would use Firebase Admin SDK:
      // const userRecord = await admin.auth().getUserByEmail(email);
      // return { exists: true, uid: userRecord.uid, displayName: userRecord.displayName };

      // For now, return a placeholder that assumes the user exists
      return {
        exists: true,
        uid: `auth-user-${Date.now()}`, // Placeholder UID
        displayName: email.split('@')[0] // Use email prefix as display name
      };
    } catch (error) {
      console.error('Error checking Firebase Auth user:', error);
      return { exists: false };
    }
  }

  /**
   * Validate if an email corresponds to an active user
   */
  static async validateRecipientEmail(email: string): Promise<{
    isValid: boolean;
    user: UserLookup | null;
    error?: string;
  }> {
    try {
      // Basic email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return {
          isValid: false,
          user: null,
          error: 'Invalid email format'
        };
      }

      // Check if user exists in our lookup table
      let user = await this.findUserByEmail(email);

      if (!user) {
        // Fallback: Check if user exists in Firebase Auth
        const authCheck = await this.checkFirebaseAuthUser(email);

        if (authCheck.exists) {
          // User exists in Firebase Auth but not in lookup table
          // Create a lookup entry for them
          try {
            await this.registerUser(
              authCheck.uid || `temp-${Date.now()}`,
              email,
              authCheck.displayName || null
            );

            // Try to find the user again
            user = await this.findUserByEmail(email);

            if (user) {
              console.log(`✅ Created lookup entry for existing Firebase user: ${email}`);
            }
          } catch (registerError) {
            console.error('Failed to register existing Firebase user:', registerError);
          }
        }

        if (!user) {
          return {
            isValid: false,
            user: null,
            error: 'User not found. The recipient must have an account to receive shared documents.'
          };
        }
      }

      if (!user.isActive) {
        return {
          isValid: false,
          user: null,
          error: 'User account is inactive'
        };
      }

      return {
        isValid: true,
        user
      };
    } catch (error) {
      console.error('Error validating recipient email:', error);
      return {
        isValid: false,
        user: null,
        error: 'Failed to validate recipient email'
      };
    }
  }

  /**
   * Deactivate a user (for account deletion)
   */
  static async deactivateUser(email: string): Promise<void> {
    try {
      const userLookupRef = doc(db, COLLECTIONS.USER_LOOKUP || 'userLookup', email.toLowerCase());
      await updateDoc(userLookupRef, {
        isActive: false,
        lastSeen: Timestamp.now()
      });
    } catch (error) {
      console.error('Error deactivating user:', error);
      throw new Error('Failed to deactivate user');
    }
  }

  /**
   * Get all active users (for admin purposes, with pagination)
   */
  static async getActiveUsers(limit: number = 50): Promise<UserLookup[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.USER_LOOKUP || 'userLookup'),
        where('isActive', '==', true)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => doc.data() as UserLookup);
    } catch (error) {
      console.error('Error getting active users:', error);
      return [];
    }
  }

  /**
   * Sync current user to lookup table (call this when user logs in)
   */
  static async syncCurrentUser(uid: string, email: string, displayName: string | null): Promise<void> {
    try {
      console.log(`🔄 Syncing user to lookup table: ${email}`);
      await this.registerUser(uid, email, displayName);
      console.log(`✅ User synced successfully: ${email}`);
    } catch (error) {
      console.error(`❌ Failed to sync user ${email}:`, error);
      // Don't throw error - this is a background operation
    }
  }

  /**
   * Batch sync multiple users (for migration purposes)
   */
  static async batchSyncUsers(users: Array<{
    uid: string;
    email: string;
    displayName: string | null;
  }>): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    console.log(`🔄 Starting batch sync of ${users.length} users...`);

    for (const user of users) {
      try {
        await this.registerUser(user.uid, user.email, user.displayName);
        results.success++;
        console.log(`✅ Synced: ${user.email}`);
      } catch (error) {
        results.failed++;
        const errorMsg = `Failed to sync ${user.email}: ${error}`;
        results.errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    }

    console.log(`🎉 Batch sync complete: ${results.success} success, ${results.failed} failed`);
    return results;
  }
}
