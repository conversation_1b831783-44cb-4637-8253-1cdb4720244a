import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  query, 
  where, 
  getDocs, 
  updateDoc,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import { UserLookup } from '../types/sharing';

/**
 * Service for managing user lookups and email-to-UID mapping
 */
export class UserLookupService {
  
  /**
   * Register or update a user in the lookup table
   */
  static async registerUser(uid: string, email: string, displayName: string | null = null): Promise<void> {
    try {
      const userLookupRef = doc(db, COLLECTIONS.USER_LOOKUP || 'userLookup', email.toLowerCase());
      
      const userLookup: Omit<UserLookup, 'email'> = {
        uid,
        displayName,
        lastSeen: Timestamp.now(),
        isActive: true
      };

      await setDoc(userLookupRef, {
        email: email.toLowerCase(),
        ...userLookup
      }, { merge: true });
      
    } catch (error) {
      console.error('Error registering user lookup:', error);
      throw new Error('Failed to register user lookup');
    }
  }

  /**
   * Find user UID by email address
   */
  static async findUserByEmail(email: string): Promise<UserLookup | null> {
    try {
      const userLookupRef = doc(db, COLLECTIONS.USER_LOOKUP || 'userLookup', email.toLowerCase());
      const userLookupSnap = await getDoc(userLookupRef);
      
      if (!userLookupSnap.exists()) {
        return null;
      }
      
      const userData = userLookupSnap.data() as UserLookup;
      
      // Update last seen
      await updateDoc(userLookupRef, {
        lastSeen: Timestamp.now()
      });
      
      return userData;
    } catch (error) {
      console.error('Error finding user by email:', error);
      return null;
    }
  }

  /**
   * Find user by UID
   */
  static async findUserByUid(uid: string): Promise<UserLookup | null> {
    try {
      const q = query(
        collection(db, COLLECTIONS.USER_LOOKUP || 'userLookup'),
        where('uid', '==', uid),
        where('isActive', '==', true)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }
      
      const userData = querySnapshot.docs[0].data() as UserLookup;
      return userData;
    } catch (error) {
      console.error('Error finding user by UID:', error);
      return null;
    }
  }

  /**
   * Validate if an email corresponds to an active user
   */
  static async validateRecipientEmail(email: string): Promise<{
    isValid: boolean;
    user: UserLookup | null;
    error?: string;
  }> {
    try {
      // Basic email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return {
          isValid: false,
          user: null,
          error: 'Invalid email format'
        };
      }

      // Check if user exists in our system
      const user = await this.findUserByEmail(email);
      
      if (!user) {
        return {
          isValid: false,
          user: null,
          error: 'User not found. The recipient must have an account to receive shared documents.'
        };
      }

      if (!user.isActive) {
        return {
          isValid: false,
          user: null,
          error: 'User account is inactive'
        };
      }

      return {
        isValid: true,
        user
      };
    } catch (error) {
      console.error('Error validating recipient email:', error);
      return {
        isValid: false,
        user: null,
        error: 'Failed to validate recipient email'
      };
    }
  }

  /**
   * Deactivate a user (for account deletion)
   */
  static async deactivateUser(email: string): Promise<void> {
    try {
      const userLookupRef = doc(db, COLLECTIONS.USER_LOOKUP || 'userLookup', email.toLowerCase());
      await updateDoc(userLookupRef, {
        isActive: false,
        lastSeen: Timestamp.now()
      });
    } catch (error) {
      console.error('Error deactivating user:', error);
      throw new Error('Failed to deactivate user');
    }
  }

  /**
   * Get all active users (for admin purposes, with pagination)
   */
  static async getActiveUsers(limit: number = 50): Promise<UserLookup[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.USER_LOOKUP || 'userLookup'),
        where('isActive', '==', true)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => doc.data() as UserLookup);
    } catch (error) {
      console.error('Error getting active users:', error);
      return [];
    }
  }
}
