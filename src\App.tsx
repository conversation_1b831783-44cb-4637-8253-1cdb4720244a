import React from 'react';
import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useNavigate } from 'react-router-dom';
import { Book, Chapter, Scene, ProjectType } from './types';
import { createNewBook, createNewChapter, createNewScene } from './utils/bookUtils';
import { WritingInterface } from './components/WritingInterface';
import { ProjectElementsPage } from './components/ProjectElementsPage';
import { AnalyticsPage } from './components/AnalyticsPage';
import { AuthForm } from './components/AuthForm';
import { SharedDocumentViewWrapper } from './components/SharedDocumentViewWrapper';

import { useAuth } from './hooks/useAuth';
import { useBooks } from './hooks/useBooks';
import { useUserSettings } from './hooks/useUserSettings';
import { ShareCleanupService } from './services/shareCleanupService';

function AppContent() {
  const { user, profile, loading: authLoading, signIn, signUp, signOut } = useAuth();
  const { books, loading: booksLoading, createBook, updateBook, deleteBook } = useBooks(user?.uid);
  const { settings, updateBackgroundImage } = useUserSettings(user?.uid || '');
  const [currentBook, setCurrentBook] = useState<Book | null>(null);
  const navigate = useNavigate();

  // Reset currentBook when user changes
  useEffect(() => {
    setCurrentBook(null);
  }, [user?.uid]);

  // Auto-select first book when books load
  useEffect(() => {
    if (books.length > 0 && !currentBook) {
      setCurrentBook(books[0]);
    }
  }, [books, currentBook]);

  // Initialize share cleanup service
  useEffect(() => {
    if (user) {
      // Schedule cleanup to run every 24 hours
      ShareCleanupService.schedulePeriodicCleanup(24);
    }
  }, [user]);

  // Auto-create first book for new users
  useEffect(() => {
    console.log('📚 Book creation check:', {
      booksLength: books.length,
      booksLoading,
      hasUser: !!user,
      hasProfile: !!profile,
      profileOnboardingCompleted: profile?.onboardingCompleted
    })

    if (books.length === 0 && !booksLoading && user && profile) {
      console.log('🚀 Auto-creating first book for new user')
      handleCreateBook('My First Novel', profile.fullName || 'Your Name', 'novel');
    }
  }, [books.length, booksLoading, user, profile]);

  const handleCreateBook = async (title: string, author: string, projectType: ProjectType = 'novel') => {
    try {
      const newBook = await createBook(title, author, projectType);
      if (newBook) {
        setCurrentBook(newBook);
      }
    } catch (error) {
      console.error('Error creating book:', error);
    }
  };

  const handleUpdateBook = async (updatedBook: Book) => {
    try {
      await updateBook(updatedBook);
      // Update the current book state immediately for UI responsiveness
      setCurrentBook(updatedBook);
    } catch (error) {
      console.error('Error updating book:', error);
    }
  };

  // Get the most up-to-date book data from the books array
  const getCurrentBook = () => {
    if (!currentBook) return null;
    // Find the book in the books array to get the latest data
    const latestBook = books.find(book => book.id === currentBook.id);
    return latestBook || currentBook;
  };

  const handleSelectBook = (book: Book) => {
    setCurrentBook(book);
  };

  const handleDeleteBook = async (bookId: string) => {
    try {
      await deleteBook(bookId);
      
      // If we deleted the current book, select another one
      if (currentBook?.id === bookId) {
        const remainingBooks = books.filter(book => book.id !== bookId);
        if (remainingBooks.length > 0) {
          setCurrentBook(remainingBooks[0]);
        } else {
          setCurrentBook(null);
        }
      }
    } catch (error) {
      console.error('Error deleting book:', error);
    }
  };



  const handleSignOut = async () => {
    try {
      await signOut();
      setCurrentBook(null);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Show loading screen
  if (authLoading) {
    return (
      <div className="min-h-screen bg-blue-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-serif text-blue-gray-700 mb-4">
            Loading...
          </h2>
        </div>
      </div>
    );
  }

  // Show auth form if not authenticated
  if (!user) {
    return (
      <AuthForm
        onSignIn={signIn}
        onSignUp={signUp}
        loading={authLoading}
      />
    );
  }

  // Show loading while books are loading
  if (booksLoading) {
    return (
      <div className="min-h-screen bg-blue-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-serif text-blue-gray-700 mb-4">
            Loading your books...
          </h2>
        </div>
      </div>
    );
  }

  // Don't render WritingInterface until we have a book
  if (!currentBook && books.length === 0 && !booksLoading) {
    return (
      <div className="min-h-screen bg-blue-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-serif text-blue-gray-700 mb-4">
            Setting up your workspace...
          </h2>
        </div>
      </div>
    );
  }

  if (!currentBook) {
    return (
      <div className="min-h-screen bg-blue-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-serif text-blue-gray-700 mb-4">
            Loading...
          </h2>
        </div>
      </div>
    );
  }

  return (
    <Routes>
      <Route 
        path="/" 
        element={
          getCurrentBook() ? (
            <WritingInterface
              book={getCurrentBook()!}
              books={books}
              onUpdateBook={handleUpdateBook}
              onCreateBook={handleCreateBook}
              onSelectBook={handleSelectBook}
              onDeleteBook={handleDeleteBook}
              onSignOut={handleSignOut}
              onNavigateToProjectElements={() => navigate('/project-elements')}
              onNavigateToAnalytics={() => navigate('/analytics')}
              user={user}
              backgroundImage={settings.backgroundImage}
              onBackgroundImageChange={updateBackgroundImage}
            />
          ) : (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
              <div className="text-center">
                <h2 className="text-2xl font-serif text-gray-700 mb-4">
                  Loading...
                </h2>
              </div>
            </div>
          )
        } 
      />
      <Route 
        path="/project-elements" 
        element={
          getCurrentBook() ? (
            <ProjectElementsPage
              book={getCurrentBook()!}
              onUpdateBook={handleUpdateBook}
              onBack={() => navigate('/')}
              backgroundImage={settings.backgroundImage}
            />
          ) : (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
              <div className="text-center">
                <h2 className="text-2xl font-serif text-gray-700 mb-4">
                  Loading...
                </h2>
              </div>
            </div>
          )
        } 
      />
      <Route
        path="/analytics"
        element={
          <AnalyticsPage
            userId={user?.uid || ''}
            book={getCurrentBook()}
            onBack={() => navigate('/')}
            backgroundImage={settings.backgroundImage}
          />
        }
      />
      <Route
        path="/shared/:sharedDocumentId"
        element={
          <SharedDocumentViewWrapper
            userId={(user as any)?.uid || ''}
            userName={profile?.fullName || user?.email || 'User'}
            userEmail={user?.email || ''}
            onClose={() => navigate('/')}
            backgroundImage={settings.backgroundImage}
          />
        }
      />
    </Routes>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;