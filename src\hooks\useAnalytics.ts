import { useState, useEffect, useCallback } from 'react';
import { collection, doc, getDocs, setDoc, addDoc, query, where, orderBy, serverTimestamp, Timestamp } from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import { WritingGoals, WritingStats, WritingSession } from '../types';

const convertTimestamp = (timestamp: Timestamp): string => {
  return timestamp.toDate().toISOString();
};

export const useAnalytics = (userId: string) => {
  const [goals, setGoals] = useState<WritingGoals>({ weekly: 3500 });
  const [stats, setStats] = useState<WritingStats>({ thisWeek: 0 });
  const [loading, setLoading] = useState(true);

  const getWeekKey = () => {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    return startOfWeek.toISOString().split('T')[0];
  };

  const loadData = useCallback(async () => {
    if (!userId) return;
    
    try {
      setLoading(true);
      
      // Load goals from Firebase
      const goalsQuery = query(
        collection(db, COLLECTIONS.WRITING_GOALS),
        where('userId', '==', userId)
      );
      const goalsSnapshot = await getDocs(goalsQuery);
      
      if (!goalsSnapshot.empty) {
        const goalsData = goalsSnapshot.docs[0].data();
        setGoals({ weekly: goalsData.weekly });
      }

      // Load weekly stats from localStorage (simpler approach)
      const weekKey = getWeekKey();
      const savedStats = localStorage.getItem(`weekly-stats-${userId}-${weekKey}`);
      if (savedStats) {
        setStats(JSON.parse(savedStats));
      } else {
        setStats({ thisWeek: 0 });
      }
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setLoading(false);
    }
  }, [userId]);



  const saveGoals = async (newGoals: WritingGoals) => {
    try {
      const goalsQuery = query(
        collection(db, COLLECTIONS.WRITING_GOALS),
        where('userId', '==', userId)
      );
      const goalsSnapshot = await getDocs(goalsQuery);
      
      if (goalsSnapshot.empty) {
        await addDoc(collection(db, COLLECTIONS.WRITING_GOALS), {
          userId,
          weekly: newGoals.weekly,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      } else {
        const docRef = doc(db, COLLECTIONS.WRITING_GOALS, goalsSnapshot.docs[0].id);
        await setDoc(docRef, {
          weekly: newGoals.weekly,
          updatedAt: serverTimestamp()
        }, { merge: true });
      }
      
      setGoals(newGoals);
    } catch (error) {
      console.error('Error saving goals:', error);
    }
  };

  const trackWritingSession = async (wordsWritten: number, bookId: string) => {
    if (wordsWritten <= 0 || !userId) return;

    try {
      // Update weekly stats in localStorage
      const weekKey = getWeekKey();
      const currentStats = { thisWeek: stats.thisWeek + wordsWritten };
      localStorage.setItem(`weekly-stats-${userId}-${weekKey}`, JSON.stringify(currentStats));
      setStats(currentStats);
      
      console.log(`📝 Tracked ${wordsWritten} words. Weekly total: ${currentStats.thisWeek}`);
    } catch (error) {
      console.error('Error tracking writing session:', error);
    }
  };

  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    goals,
    stats,
    loading,
    saveGoals,
    trackWritingSession,
    reload: loadData
  };
};