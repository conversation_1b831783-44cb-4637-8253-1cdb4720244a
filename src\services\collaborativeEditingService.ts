import { 
  doc, 
  updateDoc, 
  onSnapshot, 
  arrayUnion, 
  arrayRemove,
  Timestamp,
  getDoc,
  setDoc
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import type { Book, BookCollaborator, UserRole, EditChange, Scene } from '../types';

/**
 * Service for managing collaborative editing features
 */
export class CollaborativeEditingService {
  
  /**
   * Add a collaborator to a book
   */
  static async addCollaborator(
    bookId: string,
    collaboratorEmail: string,
    role: UserRole,
    addedByUserId: string,
    addedByUserName: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // First, find the user by email (using our user lookup service)
      const { UserLookupService } = await import('./userLookupService');
      const userValidation = await UserLookupService.validateRecipientEmail(collaboratorEmail);
      
      if (!userValidation.isValid || !userValidation.user) {
        return {
          success: false,
          error: userValidation.error || 'User not found'
        };
      }

      const collaborator: BookCollaborator = {
        userId: userValidation.user.uid,
        userEmail: collaboratorEmail.toLowerCase(),
        userName: userValidation.user.displayName || collaboratorEmail.split('@')[0],
        role,
        addedAt: new Date().toISOString(),
        addedBy: addedByUserId,
        isActive: true
      };

      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
      await updateDoc(bookRef, {
        collaborators: arrayUnion(collaborator),
        isCollaborative: true,
        updatedAt: new Date().toISOString()
      });

      // Create notification for the new collaborator
      try {
        const { NotificationService } = await import('./notificationService');
        await NotificationService.createNotification(
          userValidation.user.uid,
          'share_received',
          'Added as Collaborator',
          `${addedByUserName} added you as ${role} to a book`,
          {
            bookId,
            senderName: addedByUserName
          }
        );
      } catch (notificationError) {
        console.error('Failed to create collaboration notification:', notificationError);
      }

      return { success: true };
    } catch (error) {
      console.error('Error adding collaborator:', error);
      return {
        success: false,
        error: 'Failed to add collaborator'
      };
    }
  }

  /**
   * Remove a collaborator from a book
   */
  static async removeCollaborator(
    bookId: string,
    collaboratorUserId: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // Get current book data to find the collaborator
      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
      const bookSnap = await getDoc(bookRef);
      
      if (!bookSnap.exists()) {
        return {
          success: false,
          error: 'Book not found'
        };
      }

      const bookData = bookSnap.data() as Book;
      const collaboratorToRemove = bookData.collaborators?.find(
        c => c.userId === collaboratorUserId
      );

      if (!collaboratorToRemove) {
        return {
          success: false,
          error: 'Collaborator not found'
        };
      }

      await updateDoc(bookRef, {
        collaborators: arrayRemove(collaboratorToRemove),
        updatedAt: new Date().toISOString()
      });

      return { success: true };
    } catch (error) {
      console.error('Error removing collaborator:', error);
      return {
        success: false,
        error: 'Failed to remove collaborator'
      };
    }
  }

  /**
   * Update collaborator role
   */
  static async updateCollaboratorRole(
    bookId: string,
    collaboratorUserId: string,
    newRole: UserRole
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
      const bookSnap = await getDoc(bookRef);
      
      if (!bookSnap.exists()) {
        return {
          success: false,
          error: 'Book not found'
        };
      }

      const bookData = bookSnap.data() as Book;
      const updatedCollaborators = bookData.collaborators?.map(collaborator => 
        collaborator.userId === collaboratorUserId 
          ? { ...collaborator, role: newRole }
          : collaborator
      ) || [];

      await updateDoc(bookRef, {
        collaborators: updatedCollaborators,
        updatedAt: new Date().toISOString()
      });

      return { success: true };
    } catch (error) {
      console.error('Error updating collaborator role:', error);
      return {
        success: false,
        error: 'Failed to update collaborator role'
      };
    }
  }

  /**
   * Check if user has access to a book and their role
   */
  static getUserRoleInBook(book: Book, userId: string): {
    hasAccess: boolean;
    role: UserRole | null;
    isOwner: boolean;
  } {
    // Check if user is the owner
    if (book.ownerId === userId) {
      return {
        hasAccess: true,
        role: 'author',
        isOwner: true
      };
    }

    // Check if user is a collaborator
    const collaborator = book.collaborators?.find(c => c.userId === userId && c.isActive);
    
    if (collaborator) {
      return {
        hasAccess: true,
        role: collaborator.role,
        isOwner: false
      };
    }

    return {
      hasAccess: false,
      role: null,
      isOwner: false
    };
  }

  /**
   * Track an edit change made by an editor
   */
  static async trackEditChange(
    bookId: string,
    sceneId: string,
    change: Omit<EditChange, 'id' | 'timestamp'>
  ): Promise<{
    success: boolean;
    changeId?: string;
    error?: string;
  }> {
    try {
      const changeId = `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const editChange: EditChange = {
        ...change,
        id: changeId,
        timestamp: new Date().toISOString()
      };

      const sceneRef = doc(db, COLLECTIONS.BOOKS, bookId, COLLECTIONS.CHAPTERS, change.userId, COLLECTIONS.SCENES, sceneId);
      
      await updateDoc(sceneRef, {
        editChanges: arrayUnion(editChange),
        lastEditedBy: change.userId,
        lastEditedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      return {
        success: true,
        changeId
      };
    } catch (error) {
      console.error('Error tracking edit change:', error);
      return {
        success: false,
        error: 'Failed to track edit change'
      };
    }
  }

  /**
   * Accept or reject an edit change
   */
  static async reviewEditChange(
    bookId: string,
    sceneId: string,
    changeId: string,
    isAccepted: boolean,
    reviewedByUserId: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // This is a complex operation that would need to:
      // 1. Find the specific change in the scene's editChanges array
      // 2. Update its status
      // 3. If accepted, apply the change to the actual content
      // 4. Remove the change from pending changes
      
      // For now, we'll implement a simplified version
      const sceneRef = doc(db, COLLECTIONS.BOOKS, bookId, COLLECTIONS.CHAPTERS, 'chapters', COLLECTIONS.SCENES, sceneId);
      const sceneSnap = await getDoc(sceneRef);
      
      if (!sceneSnap.exists()) {
        return {
          success: false,
          error: 'Scene not found'
        };
      }

      const sceneData = sceneSnap.data() as Scene;
      const updatedChanges = sceneData.editChanges?.map(change => 
        change.id === changeId 
          ? { 
              ...change, 
              isAccepted, 
              acceptedBy: reviewedByUserId,
              acceptedAt: new Date().toISOString()
            }
          : change
      ) || [];

      await updateDoc(sceneRef, {
        editChanges: updatedChanges,
        updatedAt: new Date().toISOString()
      });

      return { success: true };
    } catch (error) {
      console.error('Error reviewing edit change:', error);
      return {
        success: false,
        error: 'Failed to review edit change'
      };
    }
  }

  /**
   * Get all books where user is a collaborator
   */
  static async getUserCollaborativeBooks(userId: string): Promise<Book[]> {
    try {
      console.log('🔍 Getting collaborative books for user:', userId);

      // Get all books and filter for ones where user is a collaborator
      const booksQuery = collection(db, COLLECTIONS.BOOKS);
      const booksSnapshot = await getDocs(booksQuery);

      console.log(`📚 Found ${booksSnapshot.docs.length} total books in database`);

      const collaborativeBooks: Book[] = [];

      for (const bookDoc of booksSnapshot.docs) {
        const bookData = bookDoc.data() as Book;
        const book = { ...bookData, id: bookDoc.id };

        console.log(`📖 Checking book: ${book.title} (ID: ${book.id})`);
        console.log(`   Owner: ${book.ownerId}`);
        console.log(`   Collaborators:`, book.collaborators);
        console.log(`   Is Collaborative:`, book.isCollaborative);

        // Check if user is a collaborator (not the owner)
        const isOwner = book.ownerId === userId;
        const isCollaborator = book.collaborators?.some(
          collaborator => {
            console.log(`   Checking collaborator: ${collaborator.userId} === ${userId} && ${collaborator.isActive}`);
            return collaborator.userId === userId && collaborator.isActive;
          }
        );

        console.log(`   Is Owner: ${isOwner}, Is Collaborator: ${isCollaborator}`);

        if (isCollaborator && !isOwner) {
          console.log(`✅ Adding ${book.title} to collaborative books`);
          collaborativeBooks.push(book);
        } else {
          console.log(`❌ Skipping ${book.title} - not a collaborator or is owner`);
        }
      }

      console.log(`🎉 Found ${collaborativeBooks.length} collaborative books for user`);
      return collaborativeBooks;
    } catch (error) {
      console.error('❌ Error getting collaborative books:', error);
      return [];
    }
  }

  /**
   * Subscribe to real-time book updates for collaboration
   */
  static subscribeToBookUpdates(
    bookId: string,
    onBookUpdate: (book: Book) => void,
    onError?: (error: Error) => void
  ) {
    const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
    
    return onSnapshot(
      bookRef,
      (docSnapshot) => {
        if (docSnapshot.exists()) {
          const bookData = docSnapshot.data() as Book;
          onBookUpdate({ ...bookData, id: docSnapshot.id });
        }
      },
      (error) => {
        console.error('Error listening to book updates:', error);
        if (onError) onError(error);
      }
    );
  }
}
