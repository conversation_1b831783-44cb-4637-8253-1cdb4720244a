import { GoogleGenerativeAI } from '@google/generative-ai';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { content } = req.body;

  if (!content) {
    return res.status(400).json({ error: 'Content is required' });
  }

  const apiKey = process.env.GEMINI_API_KEY;
  
  if (!apiKey) {
    return res.status(500).json({ error: 'API key not configured' });
  }

  try {
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash-lite-preview-06-17' });
    
    const contentSample = content.slice(-500);
    
    const prompt = `Based on this excerpt from a story: "${contentSample}"
    
    Generate a single, creative writing prompt that would help the author continue their story. 
    The prompt should:
    - Be one sentence long
    - Suggest a specific direction or development
    - Be engaging and thought-provoking
    - Build naturally from what's already written
    
    Return only the writing prompt, nothing else.`;
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const writingPrompt = response.text().trim();
    
    res.status(200).json({ prompt: writingPrompt });
  } catch (error) {
    console.error('Error generating writing prompt:', error);
    res.status(500).json({ error: 'Failed to generate writing prompt' });
  }
}