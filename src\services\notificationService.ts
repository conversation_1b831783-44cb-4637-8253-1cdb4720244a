import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs, 
  updateDoc,
  doc,
  onSnapshot,
  orderBy,
  limit,
  Timestamp,
  Unsubscribe
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';

export interface Notification {
  id: string;
  userId: string;
  type: 'share_received' | 'share_accepted' | 'share_completed' | 'share_commented' | 'share_revoked';
  title: string;
  message: string;
  data: {
    sharedDocumentId?: string;
    senderName?: string;
    bookTitle?: string;
    shareUrl?: string;
  };
  isRead: boolean;
  createdAt: Timestamp;
  readAt: Timestamp | null;
}

/**
 * Service for managing in-app notifications
 */
export class NotificationService {
  private static listeners: Map<string, Unsubscribe> = new Map();

  /**
   * Create a notification
   */
  static async createNotification(
    userId: string,
    type: Notification['type'],
    title: string,
    message: string,
    data: Notification['data'] = {}
  ): Promise<{
    success: boolean;
    notificationId?: string;
    error?: string;
  }> {
    try {
      const notification: Omit<Notification, 'id'> = {
        userId,
        type,
        title,
        message,
        data,
        isRead: false,
        createdAt: Timestamp.now(),
        readAt: null
      };

      const notificationRef = await addDoc(
        collection(db, 'notifications'),
        notification
      );

      console.log(`📢 Created notification for ${userId}: ${title}`);

      return {
        success: true,
        notificationId: notificationRef.id
      };
    } catch (error) {
      console.error('Error creating notification:', error);
      return {
        success: false,
        error: 'Failed to create notification'
      };
    }
  }

  /**
   * Get notifications for a user
   */
  static async getUserNotifications(
    userId: string,
    limitCount: number = 20,
    unreadOnly: boolean = false
  ): Promise<Notification[]> {
    try {
      let q = query(
        collection(db, 'notifications'),
        where('userId', '==', userId),
        limit(limitCount)
      );

      if (unreadOnly) {
        q = query(
          collection(db, 'notifications'),
          where('userId', '==', userId),
          where('isRead', '==', false),
          limit(limitCount)
        );
      }

      const querySnapshot = await getDocs(q);
      const notifications = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Notification));

      // Sort by createdAt descending (client-side)
      notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

      return notifications;
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return [];
    }
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const notificationRef = doc(db, 'notifications', notificationId);
      await updateDoc(notificationRef, {
        isRead: true,
        readAt: Timestamp.now()
      });

      return { success: true };
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return {
        success: false,
        error: 'Failed to mark notification as read'
      };
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  static async markAllAsRead(userId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const q = query(
        collection(db, 'notifications'),
        where('userId', '==', userId),
        where('isRead', '==', false)
      );

      const querySnapshot = await getDocs(q);
      const updatePromises = querySnapshot.docs.map(docSnapshot =>
        updateDoc(docSnapshot.ref, {
          isRead: true,
          readAt: Timestamp.now()
        })
      );

      await Promise.all(updatePromises);

      return { success: true };
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return {
        success: false,
        error: 'Failed to mark all notifications as read'
      };
    }
  }

  /**
   * Subscribe to real-time notifications
   */
  static subscribeToNotifications(
    userId: string,
    onNotificationsChange: (notifications: Notification[]) => void,
    onError?: (error: Error) => void
  ): Unsubscribe {
    const q = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      limit(50)
    );

    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        const notifications: Notification[] = [];
        querySnapshot.forEach((doc) => {
          notifications.push({ id: doc.id, ...doc.data() } as Notification);
        });

        // Sort by createdAt descending (client-side)
        notifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        onNotificationsChange(notifications);
      },
      (error) => {
        console.error('Error listening to notifications:', error);
        if (onError) onError(error);
      }
    );

    // Store the unsubscribe function
    this.listeners.set(`notifications-${userId}`, unsubscribe);
    
    return unsubscribe;
  }

  /**
   * Cleanup notification listeners
   */
  static cleanupListeners(userId?: string): void {
    if (userId) {
      const unsubscribe = this.listeners.get(`notifications-${userId}`);
      if (unsubscribe) {
        unsubscribe();
        this.listeners.delete(`notifications-${userId}`);
      }
    } else {
      // Cleanup all listeners
      this.listeners.forEach((unsubscribe) => {
        unsubscribe();
      });
      this.listeners.clear();
    }
  }

  /**
   * Get unread notification count
   */
  static async getUnreadCount(userId: string): Promise<number> {
    try {
      const q = query(
        collection(db, 'notifications'),
        where('userId', '==', userId),
        where('isRead', '==', false)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.length;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  /**
   * Convenience methods for common notification types
   */
  static async notifyShareReceived(
    recipientId: string,
    senderName: string,
    bookTitle: string,
    sharedDocumentId: string,
    shareUrl?: string
  ) {
    return this.createNotification(
      recipientId,
      'share_received',
      'Document Shared With You',
      `${senderName} shared "${bookTitle}" with you`,
      {
        sharedDocumentId,
        senderName,
        bookTitle,
        shareUrl
      }
    );
  }

  static async notifyShareAccepted(
    senderId: string,
    recipientName: string,
    bookTitle: string,
    sharedDocumentId: string
  ) {
    return this.createNotification(
      senderId,
      'share_accepted',
      'Share Accepted',
      `${recipientName} accepted your shared document "${bookTitle}"`,
      {
        sharedDocumentId,
        senderName: recipientName,
        bookTitle
      }
    );
  }

  static async notifyShareCompleted(
    senderId: string,
    recipientName: string,
    bookTitle: string,
    sharedDocumentId: string
  ) {
    return this.createNotification(
      senderId,
      'share_completed',
      'Edits Completed',
      `${recipientName} completed editing "${bookTitle}"`,
      {
        sharedDocumentId,
        senderName: recipientName,
        bookTitle
      }
    );
  }

  static async notifyShareCommented(
    userId: string,
    commenterName: string,
    bookTitle: string,
    sharedDocumentId: string
  ) {
    return this.createNotification(
      userId,
      'share_commented',
      'New Comment',
      `${commenterName} commented on "${bookTitle}"`,
      {
        sharedDocumentId,
        senderName: commenterName,
        bookTitle
      }
    );
  }

  static async notifyShareRevoked(
    recipientId: string,
    senderName: string,
    bookTitle: string,
    sharedDocumentId: string
  ) {
    return this.createNotification(
      recipientId,
      'share_revoked',
      'Share Revoked',
      `${senderName} revoked access to "${bookTitle}"`,
      {
        sharedDocumentId,
        senderName,
        bookTitle
      }
    );
  }
}
