import React, { useState } from 'react';
import { X, BookOpen, Crown, Edit, Eye, Users, RefreshCw, Calendar } from 'lucide-react';
import { useCollaborativeBooks } from '../hooks/useCollaborativeBooks';
import type { Book, UserRole } from '../types';

interface CollaborativeBooksModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  onOpenBook: (book: Book) => void;
  backgroundImage: string;
}

export const CollaborativeBooksModal: React.FC<CollaborativeBooksModalProps> = ({
  isOpen,
  onClose,
  userId,
  onOpenBook,
  backgroundImage
}) => {
  const {
    collaborativeBooks,
    loading,
    error,
    refreshCollaborativeBooks,
    getUserRoleInBook
  } = useCollaborativeBooks(userId);

  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    console.log('🔄 Manual refresh triggered');
    setRefreshing(true);
    await refreshCollaborativeBooks();
    setRefreshing(false);
  };

  const handleOpenBook = (book: Book) => {
    onOpenBook(book);
    onClose();
  };

  // Refresh when modal opens
  React.useEffect(() => {
    if (isOpen) {
      console.log('🔄 Modal opened, refreshing collaborative books');
      refreshCollaborativeBooks();
    }
  }, [isOpen, refreshCollaborativeBooks]);

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'author':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'editor':
        return <Edit className="w-4 h-4 text-blue-500" />;
      case 'viewer':
        return <Eye className="w-4 h-4 text-gray-500" />;
      default:
        return <Users className="w-4 h-4 text-gray-500" />;
    }
  };

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case 'author':
        return 'text-yellow-600 bg-yellow-100';
      case 'editor':
        return 'text-blue-600 bg-blue-100';
      case 'viewer':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      <div className="relative z-10 w-full max-w-4xl bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Users className="w-6 h-6 text-white mr-3" />
            <h2 className="text-2xl font-serif font-semibold text-white">Collaborative Books</h2>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="p-2 text-white/60 hover:text-white transition-colors disabled:opacity-50"
              title="Refresh"
            >
              <RefreshCw className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />
            </button>
            <button 
              onClick={onClose}
              className="text-white/60 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-lg">
            <p className="text-red-700 font-sans text-sm">{error}</p>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-6 h-6 text-white animate-spin mr-3" />
            <span className="text-white font-sans">Loading collaborative books...</span>
          </div>
        )}

        {/* Books List */}
        {!loading && (
          <div className="space-y-4 overflow-y-auto max-h-96">
            {collaborativeBooks.length === 0 ? (
              <div className="text-center py-12">
                <BookOpen className="w-12 h-12 text-white/40 mx-auto mb-4" />
                <h3 className="text-lg font-serif text-white mb-2">No Collaborative Books</h3>
                <p className="text-white/60 font-sans">
                  You haven't been added as a collaborator to any books yet.
                </p>
              </div>
            ) : (
              <>
                <div className="mb-4">
                  <p className="text-white/80 font-sans text-sm">
                    You have access to {collaborativeBooks.length} collaborative book{collaborativeBooks.length !== 1 ? 's' : ''}
                  </p>
                </div>

                {collaborativeBooks.map((book) => {
                  const roleInfo = getUserRoleInBook(book.id);
                  const role = roleInfo?.role || 'viewer';

                  return (
                    <div
                      key={book.id}
                      className="p-4 bg-white/5 border border-white/10 rounded-xl hover:bg-white/10 transition-colors cursor-pointer"
                      onClick={() => handleOpenBook(book)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center mb-2">
                            <BookOpen className="w-5 h-5 text-white mr-3" />
                            <h3 className="text-lg font-serif text-white font-semibold">
                              {book.title}
                            </h3>
                          </div>
                          
                          <div className="space-y-1 text-sm font-sans">
                            <p className="text-white/80">
                              by {book.author}
                            </p>
                            <p className="text-white/60">
                              {book.wordCount.toLocaleString()} words • {book.chapters?.length || 0} chapters
                            </p>
                            <div className="flex items-center text-white/60">
                              <Calendar className="w-3 h-3 mr-1" />
                              <span>Updated {formatDate(book.updatedAt)}</span>
                            </div>
                          </div>

                          {/* Collaborators Count */}
                          <div className="mt-2 flex items-center text-white/60 text-sm font-sans">
                            <Users className="w-3 h-3 mr-1" />
                            <span>
                              {(book.collaborators?.filter(c => c.isActive).length || 0) + 1} collaborator{((book.collaborators?.filter(c => c.isActive).length || 0) + 1) !== 1 ? 's' : ''}
                            </span>
                          </div>
                        </div>

                        <div className="flex flex-col items-end space-y-2">
                          {/* User's Role */}
                          <div className="flex items-center">
                            {getRoleIcon(role)}
                            <span className={`ml-2 px-2 py-1 rounded-full text-xs font-sans ${getRoleColor(role)}`}>
                              {role}
                            </span>
                          </div>

                          {/* Open Button */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleOpenBook(book);
                            }}
                            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-sans transition-colors"
                          >
                            Open Book
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </>
            )}
          </div>
        )}

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-white/10">
          <p className="text-white/60 font-sans text-sm text-center">
            Books where you've been added as a collaborator will appear here
          </p>
        </div>
      </div>
    </div>
  );
};
