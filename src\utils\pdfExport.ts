import jsPDF from 'jspdf';
import { Book } from '../types';

export const exportBookToPDF = (book: Book) => {
  const pdf = new jsPDF();
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 20;
  const lineHeight = 7;
  let yPosition = margin;

  // Helper function to add new page if needed
  const checkPageBreak = (requiredSpace: number = lineHeight) => {
    if (yPosition + requiredSpace > pageHeight - margin) {
      pdf.addPage();
      yPosition = margin;
    }
  };

  // Helper function to add text with word wrapping
  const addWrappedText = (text: string, fontSize: number, isBold: boolean = false) => {
    pdf.setFontSize(fontSize);
    if (isBold) {
      pdf.setFont('helvetica', 'bold');
    } else {
      pdf.setFont('helvetica', 'normal');
    }

    const lines = pdf.splitTextToSize(text, pageWidth - 2 * margin);
    
    for (const line of lines) {
      checkPageBreak();
      pdf.text(line, margin, yPosition);
      yPosition += lineHeight;
    }
  };

  // Title page
  pdf.setFontSize(24);
  pdf.setFont('helvetica', 'bold');
  const titleLines = pdf.splitTextToSize(book.title, pageWidth - 2 * margin);
  const titleHeight = titleLines.length * 10;
  const titleY = (pageHeight - titleHeight - 20) / 2;
  
  titleLines.forEach((line: string, index: number) => {
    pdf.text(line, pageWidth / 2, titleY + (index * 10), { align: 'center' });
  });

  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`by ${book.author}`, pageWidth / 2, titleY + titleHeight + 20, { align: 'center' });

  // Add new page for content
  pdf.addPage();
  yPosition = margin;

  // Export chapters and scenes
  book.chapters
    .sort((a, b) => a.order - b.order)
    .forEach((chapter) => {
      // Chapter heading
      checkPageBreak(20);
      yPosition += 10; // Extra space before chapter
      addWrappedText(chapter.title, 18, true);
      yPosition += 5; // Space after chapter title

      // Chapter scenes
      chapter.scenes
        .sort((a, b) => a.order - b.order)
        .forEach((scene, sceneIndex) => {
          // Scene divider (except for first scene)
          if (sceneIndex > 0) {
            checkPageBreak(15);
            yPosition += 5;
            pdf.setLineWidth(0.5);
            pdf.line(margin, yPosition, pageWidth - margin, yPosition);
            yPosition += 10;
          }

          // Scene content
          if (scene.content.trim()) {
            // Remove HTML tags and decode entities
            const cleanContent = scene.content
              .replace(/<[^>]*>/g, '')
              .replace(/&nbsp;/g, ' ')
              .replace(/&amp;/g, '&')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&quot;/g, '"')
              .trim();

            if (cleanContent) {
              addWrappedText(cleanContent, 12);
              yPosition += 5; // Space after scene
            }
          }
        });

      yPosition += 10; // Extra space after chapter
    });

  // Save the PDF
  const fileName = `${book.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`;
  pdf.save(fileName);
};