import { initializeApp } from 'firebase/app'
import { getAuth } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'

const firebaseConfig = {
  apiKey: "AIzaSyA-mEyekBn9eqc2OVciUM5qOD1u3DGH72E",
  authDomain: "writerone-3016a.firebaseapp.com",
  projectId: "writerone-3016a",
  storageBucket: "writerone-3016a.firebasestorage.app",
  messagingSenderId: "284057014908",
  appId: "1:284057014908:web:75f239a2e3a583de6e2ff9",
  measurementId: "G-WPT3EX503C"
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)

// Initialize Firebase services
export const auth = getAuth(app)
export const db = getFirestore(app)
