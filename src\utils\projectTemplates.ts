import { ProjectType, Chapter, Character, PlotPoint } from '../types';
import { createNew<PERSON>hapter, createNewCharacter, createNewPlotPoint, generateId } from './bookUtils';

interface ProjectTemplate {
  chapters: Omit<Chapter, 'id' | 'bookId' | 'createdAt' | 'updatedAt'>[];
  characters: Omit<Character, 'id' | 'bookId' | 'createdAt' | 'updatedAt'>[];
  plotPoints: Omit<PlotPoint, 'id' | 'bookId' | 'createdAt' | 'updatedAt'>[];
}

export const getProjectTemplate = (projectType: ProjectType): ProjectTemplate => {
  switch (projectType) {
    case 'novel':
      return {
        chapters: [
          {
            title: 'Chapter 1',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Chapter 2',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 1,
            orderIndex: 1
          }
        ],
        characters: [
          {
            name: 'Protagonist',
            description: 'The main character of your story',
            role: 'Main Character',
            notes: '',
            appearance: '',
            personality: '',
            backstory: '',
            goals: ''
          }
        ],
        plotPoints: [
          {
            title: 'Opening Hook',
            description: 'The compelling opening that draws readers in',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Inciting Incident',
            description: 'The event that sets the story in motion',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 1,
            orderIndex: 1
          },
          {
            title: 'Climax',
            description: 'The turning point of your story',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 2,
            orderIndex: 2
          },
          {
            title: 'Resolution',
            description: 'How the story concludes',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 3,
            orderIndex: 3
          }
        ]
      };

    case 'novella':
      return {
        chapters: [
          {
            title: 'Part 1',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Part 2',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 1,
            orderIndex: 1
          },
          {
            title: 'Part 3',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 2,
            orderIndex: 2
          }
        ],
        characters: [
          {
            name: 'Main Character',
            description: 'The central figure of your novella',
            role: 'Protagonist',
            notes: '',
            appearance: '',
            personality: '',
            backstory: '',
            goals: ''
          }
        ],
        plotPoints: [
          {
            title: 'Setup',
            description: 'Establish the character and situation',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Conflict',
            description: 'The central problem or challenge',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 1,
            orderIndex: 1
          },
          {
            title: 'Resolution',
            description: 'How the conflict is resolved',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 2,
            orderIndex: 2
          }
        ]
      };

    case 'short-story':
      return {
        chapters: [
          {
            title: 'Story',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 0,
            orderIndex: 0
          }
        ],
        characters: [
          {
            name: 'Main Character',
            description: 'The protagonist of your short story',
            role: 'Protagonist',
            notes: '',
            appearance: '',
            personality: '',
            backstory: '',
            goals: ''
          }
        ],
        plotPoints: [
          {
            title: 'Opening',
            description: 'Hook the reader immediately',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Turning Point',
            description: 'The moment everything changes',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 1,
            orderIndex: 1
          },
          {
            title: 'Conclusion',
            description: 'A satisfying ending',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 2,
            orderIndex: 2
          }
        ]
      };

    case 'poem':
      return {
        chapters: [
          {
            title: 'Collection',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 0,
            orderIndex: 0
          }
        ],
        characters: [],
        plotPoints: [
          {
            title: 'Theme Exploration',
            description: 'Central themes to explore in your poetry',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Emotional Arc',
            description: 'The emotional journey of your collection',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 1,
            orderIndex: 1
          }
        ]
      };

    case 'screenplay':
      return {
        chapters: [
          {
            title: 'Act I',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Act II',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 1,
            orderIndex: 1
          },
          {
            title: 'Act III',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 2,
            orderIndex: 2
          }
        ],
        characters: [
          {
            name: 'Protagonist',
            description: 'The main character of your screenplay',
            role: 'Lead',
            notes: '',
            appearance: '',
            personality: '',
            backstory: '',
            goals: ''
          }
        ],
        plotPoints: [
          {
            title: 'Opening Image',
            description: 'The first impression of your story world',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Plot Point 1',
            description: 'End of Act I - the story really begins',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 1,
            orderIndex: 1
          },
          {
            title: 'Midpoint',
            description: 'The story shifts direction',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 2,
            orderIndex: 2
          },
          {
            title: 'Plot Point 2',
            description: 'End of Act II - final push to resolution',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 3,
            orderIndex: 3
          },
          {
            title: 'Climax',
            description: 'The final confrontation',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 4,
            orderIndex: 4
          }
        ]
      };

    case 'essay':
      return {
        chapters: [
          {
            title: 'Essay 1',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 0,
            orderIndex: 0
          }
        ],
        characters: [],
        plotPoints: [
          {
            title: 'Thesis',
            description: 'Your main argument or point',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Supporting Evidence',
            description: 'Research and examples that support your thesis',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 1,
            orderIndex: 1
          },
          {
            title: 'Conclusion',
            description: 'Synthesis and final thoughts',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 2,
            orderIndex: 2
          }
        ]
      };

    case 'journal':
      return {
        chapters: [
          {
            title: 'Reflections',
            content: '',
            wordCount: 0,
            scenes: [],
            order: 0,
            orderIndex: 0
          }
        ],
        characters: [],
        plotPoints: [
          {
            title: 'Personal Growth',
            description: 'Areas of personal development to explore',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 0,
            orderIndex: 0
          },
          {
            title: 'Life Events',
            description: 'Significant moments to document',
            chapterId: null,
            sceneId: null,
            completed: false,
            order: 1,
            orderIndex: 1
          }
        ]
      };

    default:
      return {
        chapters: [],
        characters: [],
        plotPoints: []
      };
  }
};
