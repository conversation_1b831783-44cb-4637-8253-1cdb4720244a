import React, { useState, useEffect } from 'react';
import { X, Plus, Users, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { UserLookupService } from '../services/userLookupService';
import { syncCurrentUserToLookup, manuallyAddUserToLookup, checkUserInLookup, getAllLookupUsers } from '../utils/userSyncUtils';

interface UserManagementPanelProps {
  isOpen: boolean;
  onClose: () => void;
  backgroundImage: string;
}

export const UserManagementPanel: React.FC<UserManagementPanelProps> = ({
  isOpen,
  onClose,
  backgroundImage
}) => {
  const [users, setUsers] = useState<any[]>([]);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserName, setNewUserName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [checkEmail, setCheckEmail] = useState('');
  const [checkResult, setCheckResult] = useState<any>(null);

  useEffect(() => {
    if (isOpen) {
      loadUsers();
    }
  }, [isOpen]);

  const loadUsers = async () => {
    setIsLoading(true);
    try {
      const allUsers = await getAllLookupUsers();
      setUsers(allUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      setMessage({ type: 'error', text: 'Failed to load users' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncCurrentUser = async () => {
    setIsLoading(true);
    try {
      const result = await syncCurrentUserToLookup();
      if (result.success) {
        setMessage({ type: 'success', text: 'Current user synced successfully' });
        loadUsers();
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to sync user' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to sync current user' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newUserEmail.trim()) return;

    setIsLoading(true);
    try {
      const result = await manuallyAddUserToLookup(newUserEmail.trim(), newUserName.trim() || undefined);
      if (result.success) {
        setMessage({ type: 'success', text: `User ${newUserEmail} added successfully` });
        setNewUserEmail('');
        setNewUserName('');
        loadUsers();
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to add user' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to add user' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCheckUser = async () => {
    if (!checkEmail.trim()) return;

    try {
      const result = await checkUserInLookup(checkEmail.trim());
      setCheckResult(result);
    } catch (error) {
      setCheckResult({ exists: false, error: 'Failed to check user' });
    }
  };

  const clearMessage = () => {
    setTimeout(() => setMessage(null), 5000);
  };

  useEffect(() => {
    if (message) {
      clearMessage();
    }
  }, [message]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      <div className="relative z-10 w-full max-w-4xl bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-serif font-semibold text-white">User Management</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {message && (
          <div className={`mb-4 p-3 rounded-lg border ${
            message.type === 'success' 
              ? 'bg-green-100 border-green-300 text-green-700' 
              : 'bg-red-100 border-red-300 text-red-700'
          }`}>
            <div className="flex items-center">
              {message.type === 'success' ? (
                <CheckCircle className="w-4 h-4 mr-2" />
              ) : (
                <AlertCircle className="w-4 h-4 mr-2" />
              )}
              <span className="font-sans text-sm">{message.text}</span>
            </div>
          </div>
        )}

        <div className="space-y-6 overflow-y-auto max-h-96">
          {/* Sync Current User */}
          <div className="p-4 bg-white/5 border border-white/10 rounded-xl">
            <h3 className="text-lg font-serif text-white mb-3">Sync Current User</h3>
            <p className="text-white/60 font-sans text-sm mb-3">
              Add the currently logged-in user to the lookup table for sharing.
            </p>
            <button
              onClick={handleSyncCurrentUser}
              disabled={isLoading}
              className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-lg font-sans transition-colors"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Sync Current User
            </button>
          </div>

          {/* Add User Manually */}
          <div className="p-4 bg-white/5 border border-white/10 rounded-xl">
            <h3 className="text-lg font-serif text-white mb-3">Add User Manually</h3>
            <form onSubmit={handleAddUser} className="space-y-3">
              <div>
                <label className="block text-white/80 font-sans text-sm mb-1">Email</label>
                <input
                  type="email"
                  value={newUserEmail}
                  onChange={(e) => setNewUserEmail(e.target.value)}
                  className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 font-sans"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <label className="block text-white/80 font-sans text-sm mb-1">Display Name (Optional)</label>
                <input
                  type="text"
                  value={newUserName}
                  onChange={(e) => setNewUserName(e.target.value)}
                  className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 font-sans"
                  placeholder="User Name"
                />
              </div>
              <button
                type="submit"
                disabled={isLoading || !newUserEmail.trim()}
                className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white rounded-lg font-sans transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add User
              </button>
            </form>
          </div>

          {/* Check User */}
          <div className="p-4 bg-white/5 border border-white/10 rounded-xl">
            <h3 className="text-lg font-serif text-white mb-3">Check User</h3>
            <div className="flex space-x-2 mb-3">
              <input
                type="email"
                value={checkEmail}
                onChange={(e) => setCheckEmail(e.target.value)}
                className="flex-1 px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 font-sans"
                placeholder="<EMAIL>"
              />
              <button
                onClick={handleCheckUser}
                disabled={!checkEmail.trim()}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white rounded-lg font-sans transition-colors"
              >
                Check
              </button>
            </div>
            {checkResult && (
              <div className={`p-3 rounded-lg ${
                checkResult.exists ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
              }`}>
                <p className="font-sans text-sm">
                  {checkResult.exists ? (
                    <>
                      ✅ User exists in lookup table
                      {checkResult.user && (
                        <span className="block mt-1">
                          UID: {checkResult.user.uid}<br/>
                          Name: {checkResult.user.displayName || 'N/A'}
                        </span>
                      )}
                    </>
                  ) : (
                    '❌ User not found in lookup table'
                  )}
                </p>
              </div>
            )}
          </div>

          {/* Current Users */}
          <div className="p-4 bg-white/5 border border-white/10 rounded-xl">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-serif text-white">Current Users ({users.length})</h3>
              <button
                onClick={loadUsers}
                disabled={isLoading}
                className="flex items-center px-3 py-1 bg-white/20 hover:bg-white/30 text-white rounded-lg font-sans text-sm transition-colors"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Refresh
              </button>
            </div>
            <div className="max-h-40 overflow-y-auto space-y-2">
              {isLoading ? (
                <div className="text-white/60 font-sans text-sm">Loading...</div>
              ) : users.length === 0 ? (
                <div className="text-white/60 font-sans text-sm">No users found</div>
              ) : (
                users.map((user, index) => (
                  <div key={index} className="p-2 bg-white/10 rounded-lg">
                    <div className="text-white font-sans text-sm">{user.email}</div>
                    <div className="text-white/60 font-sans text-xs">
                      {user.displayName || 'No display name'} • UID: {user.uid}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
