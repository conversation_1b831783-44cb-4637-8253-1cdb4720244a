import React, { useState, useEffect } from 'react';
import { X, Shield, Clock, Users, Activity, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { useDocumentSharing } from '../hooks/useDocumentSharing';
import { ShareActivityService } from '../services/shareActivityService';
import type { SharedDocument, ShareActivity } from '../types/sharing';

interface ShareManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
  userName: string;
  userEmail: string;
  backgroundImage: string;
}

export const ShareManagementModal: React.FC<ShareManagementModalProps> = ({
  isOpen,
  onClose,
  userId,
  userName,
  userEmail,
  backgroundImage
}) => {
  const [activeTab, setActiveTab] = useState<'active' | 'activity'>('active');
  const [activeShares, setActiveShares] = useState<SharedDocument[]>([]);
  const [recentActivity, setRecentActivity] = useState<ShareActivity[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { 
    getSharedByMe, 
    revokeSharedDocument, 
    loading: sharingLoading 
  } = useDocumentSharing({ userId, userName, userEmail });

  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen, activeTab]);

  const loadData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (activeTab === 'active') {
        const shares = await getSharedByMe(['pending', 'accepted']);
        setActiveShares(shares);
      } else {
        const activity = await ShareActivityService.getUserActivityLog(userId, 50);
        setRecentActivity(activity);
      }
    } catch (err) {
      console.error('Error loading share management data:', err);
      setError('Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRevokeShare = async (shareId: string, reason?: string) => {
    try {
      const result = await revokeSharedDocument(shareId, reason);
      if (result.success) {
        // Refresh the data
        loadData();
      } else {
        setError(result.error || 'Failed to revoke share');
      }
    } catch (err) {
      console.error('Error revoking share:', err);
      setError('Failed to revoke share');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'accepted':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case 'declined':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'revoked':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getActivityIcon = (action: string) => {
    switch (action) {
      case 'created':
        return <Users className="w-4 h-4 text-blue-500" />;
      case 'accepted':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'viewed':
        return <Activity className="w-4 h-4 text-gray-500" />;
      case 'edited':
        return <Activity className="w-4 h-4 text-purple-500" />;
      case 'commented':
        return <Activity className="w-4 h-4 text-indigo-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      case 'declined':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'revoked':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      <div className="relative z-10 w-full max-w-4xl bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-serif font-semibold text-white">Share Management</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6 bg-white/10 rounded-xl p-1">
          <button
            onClick={() => setActiveTab('active')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg font-sans transition-colors ${
              activeTab === 'active'
                ? 'bg-white/20 text-white'
                : 'text-white/60 hover:text-white/80'
            }`}
          >
            <Shield className="w-4 h-4 mr-2" />
            Active Shares
          </button>
          <button
            onClick={() => setActiveTab('activity')}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg font-sans transition-colors ${
              activeTab === 'activity'
                ? 'bg-white/20 text-white'
                : 'text-white/60 hover:text-white/80'
            }`}
          >
            <Activity className="w-4 h-4 mr-2" />
            Recent Activity
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-lg">
            <p className="text-red-700 font-sans text-sm">{error}</p>
          </div>
        )}

        <div className="overflow-y-auto max-h-96">
          {activeTab === 'active' ? (
            <div className="space-y-3">
              {isLoading || sharingLoading ? (
                <div className="text-center py-8">
                  <div className="text-white/60 font-sans">Loading...</div>
                </div>
              ) : activeShares.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-white/60 font-sans">No active shares found</div>
                </div>
              ) : (
                activeShares.map((share) => (
                  <div 
                    key={share.id} 
                    className="p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          {getStatusIcon(share.status)}
                          <h3 className="text-lg font-serif text-white ml-2">{share.bookTitle}</h3>
                        </div>
                        <p className="text-white/60 text-sm font-sans mb-1">
                          Shared with: {share.recipientEmail}
                        </p>
                        <p className="text-white/60 text-sm font-sans mb-1">
                          Permission: {share.permission}
                        </p>
                        <p className="text-white/60 text-sm font-sans mb-1">
                          Created: {formatDate(share.createdAt)}
                        </p>
                        {share.expiresAt && (
                          <p className="text-white/60 text-sm font-sans mb-1">
                            Expires: {formatDate(share.expiresAt)}
                          </p>
                        )}
                        <p className="text-white/60 text-sm font-sans">
                          Status: {share.status.charAt(0).toUpperCase() + share.status.slice(1)}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        {(share.status === 'pending' || share.status === 'accepted') && (
                          <button
                            onClick={() => handleRevokeShare(share.id, 'Revoked by sender')}
                            className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm font-sans transition-colors"
                          >
                            Revoke
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="text-white/60 font-sans">Loading...</div>
                </div>
              ) : recentActivity.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-white/60 font-sans">No recent activity found</div>
                </div>
              ) : (
                recentActivity.map((activity) => (
                  <div 
                    key={activity.id} 
                    className="p-3 bg-white/5 border border-white/10 rounded-xl"
                  >
                    <div className="flex items-start">
                      {getActivityIcon(activity.action)}
                      <div className="ml-3 flex-1">
                        <p className="text-white font-sans text-sm">
                          <span className="font-medium">{activity.userName}</span> {activity.action} a document
                        </p>
                        {activity.details && (
                          <p className="text-white/60 font-sans text-xs mt-1">{activity.details}</p>
                        )}
                        <p className="text-white/40 font-sans text-xs mt-1">
                          {formatDate(activity.timestamp)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
