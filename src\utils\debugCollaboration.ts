import { collection, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import type { Book } from '../types';

/**
 * Debug utilities for collaboration features
 */

/**
 * Debug collaborative books for a specific user
 */
export async function debugCollaborativeBooks(userId: string) {
  console.log('🔍 DEBUG: Collaborative books for user:', userId);
  console.log('='.repeat(60));
  
  try {
    // Get all books
    const booksQuery = collection(db, COLLECTIONS.BOOKS);
    const booksSnapshot = await getDocs(booksQuery);
    
    console.log(`📚 Total books in database: ${booksSnapshot.docs.length}`);
    console.log('');
    
    let ownedBooks = 0;
    let collaborativeBooks = 0;
    let booksWithCollaborators = 0;
    
    for (const bookDoc of booksSnapshot.docs) {
      const bookData = bookDoc.data() as Book;
      const book = { ...bookData, id: bookDoc.id };
      
      const isOwner = book.ownerId === userId;
      const hasCollaborators = book.collaborators && book.collaborators.length > 0;
      const isCollaborator = book.collaborators?.some(
        collaborator => collaborator.userId === userId && collaborator.isActive
      );
      
      if (isOwner) {
        ownedBooks++;
      }
      
      if (hasCollaborators) {
        booksWithCollaborators++;
      }
      
      if (isCollaborator && !isOwner) {
        collaborativeBooks++;
      }
      
      // Log detailed info for books involving this user
      if (isOwner || isCollaborator || hasCollaborators) {
        console.log(`📖 "${book.title}" (ID: ${book.id})`);
        console.log(`   Owner: ${book.ownerId} ${isOwner ? '(YOU)' : ''}`);
        console.log(`   Is Collaborative: ${book.isCollaborative}`);
        console.log(`   Collaborators (${book.collaborators?.length || 0}):`);
        
        if (book.collaborators && book.collaborators.length > 0) {
          book.collaborators.forEach((collaborator, index) => {
            const isYou = collaborator.userId === userId;
            console.log(`     ${index + 1}. ${collaborator.userName} (${collaborator.userEmail})`);
            console.log(`        Role: ${collaborator.role}`);
            console.log(`        Active: ${collaborator.isActive}`);
            console.log(`        User ID: ${collaborator.userId} ${isYou ? '(YOU)' : ''}`);
          });
        } else {
          console.log(`     (none)`);
        }
        
        console.log(`   Your status: ${isOwner ? 'OWNER' : isCollaborator ? 'COLLABORATOR' : 'NOT INVOLVED'}`);
        console.log('');
      }
    }
    
    console.log('📊 SUMMARY:');
    console.log(`   Books you own: ${ownedBooks}`);
    console.log(`   Books you collaborate on: ${collaborativeBooks}`);
    console.log(`   Total books with collaborators: ${booksWithCollaborators}`);
    console.log('');
    
    if (collaborativeBooks === 0) {
      console.log('❌ ISSUE: No collaborative books found!');
      console.log('   Possible causes:');
      console.log('   1. User hasn\'t been added as collaborator to any books');
      console.log('   2. User ID mismatch in collaborators array');
      console.log('   3. Collaborator is marked as inactive');
      console.log('   4. Migration hasn\'t been run on books');
    } else {
      console.log('✅ Collaborative books found successfully!');
    }
    
  } catch (error) {
    console.error('❌ Error debugging collaborative books:', error);
  }
}

/**
 * Debug a specific book's collaboration setup
 */
export async function debugBookCollaboration(bookId: string) {
  console.log('🔍 DEBUG: Book collaboration for book:', bookId);
  console.log('='.repeat(60));
  
  try {
    const bookRef = doc(db, COLLECTIONS.BOOKS, bookId);
    const bookSnap = await getDoc(bookRef);
    
    if (!bookSnap.exists()) {
      console.log('❌ Book not found!');
      return;
    }
    
    const bookData = bookSnap.data() as Book;
    const book = { ...bookData, id: bookSnap.id };
    
    console.log(`📖 Book: "${book.title}"`);
    console.log(`   ID: ${book.id}`);
    console.log(`   Owner: ${book.ownerId}`);
    console.log(`   Is Collaborative: ${book.isCollaborative}`);
    console.log(`   Created: ${book.createdAt}`);
    console.log(`   Updated: ${book.updatedAt}`);
    console.log('');
    
    console.log(`👥 Collaborators (${book.collaborators?.length || 0}):`);
    if (book.collaborators && book.collaborators.length > 0) {
      book.collaborators.forEach((collaborator, index) => {
        console.log(`   ${index + 1}. ${collaborator.userName}`);
        console.log(`      Email: ${collaborator.userEmail}`);
        console.log(`      User ID: ${collaborator.userId}`);
        console.log(`      Role: ${collaborator.role}`);
        console.log(`      Active: ${collaborator.isActive}`);
        console.log(`      Added: ${collaborator.addedAt}`);
        console.log(`      Added by: ${collaborator.addedBy}`);
        console.log('');
      });
    } else {
      console.log('   (none)');
    }
    
  } catch (error) {
    console.error('❌ Error debugging book collaboration:', error);
  }
}

/**
 * Debug user lookup table
 */
export async function debugUserLookup() {
  console.log('🔍 DEBUG: User lookup table');
  console.log('='.repeat(60));
  
  try {
    const userLookupQuery = collection(db, 'userLookup');
    const userLookupSnapshot = await getDocs(userLookupQuery);
    
    console.log(`👥 Users in lookup table: ${userLookupSnapshot.docs.length}`);
    console.log('');
    
    userLookupSnapshot.docs.forEach((doc, index) => {
      const data = doc.data();
      console.log(`${index + 1}. ${data.email}`);
      console.log(`   UID: ${data.uid}`);
      console.log(`   Name: ${data.displayName || 'N/A'}`);
      console.log(`   Active: ${data.isActive}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ Error debugging user lookup:', error);
  }
}

/**
 * Run all collaboration debugging
 */
export async function debugAllCollaboration(userId: string) {
  console.log('🚀 FULL COLLABORATION DEBUG');
  console.log('='.repeat(80));
  
  await debugUserLookup();
  console.log('\n' + '='.repeat(80) + '\n');
  
  await debugCollaborativeBooks(userId);
  console.log('\n' + '='.repeat(80));
  
  console.log('🎉 Debug complete!');
}

/**
 * Quick test to add yourself as collaborator to a book (for testing)
 */
export async function testAddCollaborator(bookId: string, userEmail: string, userId: string) {
  console.log('🧪 TEST: Adding collaborator for testing');

  try {
    const { CollaborativeEditingService } = await import('../services/collaborativeEditingService');

    const result = await CollaborativeEditingService.addCollaborator(
      bookId,
      userEmail,
      'editor',
      userId,
      'Test User'
    );

    console.log('Test result:', result);

    if (result.success) {
      console.log('✅ Test collaborator added successfully!');
      console.log('Now run: debugCollaborativeBooks("' + userId + '")');
    } else {
      console.log('❌ Failed to add test collaborator:', result.error);
    }

  } catch (error) {
    console.error('❌ Error in test:', error);
  }
}

/**
 * Test loading collaborative book data
 */
export async function testCollaborativeBookData(userId: string) {
  console.log('🧪 TEST: Loading collaborative book data for user:', userId);

  try {
    const { CollaborativeEditingService } = await import('../services/collaborativeEditingService');

    const books = await CollaborativeEditingService.getUserCollaborativeBooks(userId);

    console.log(`📚 Found ${books.length} collaborative books`);

    books.forEach((book, index) => {
      console.log(`\n📖 Book ${index + 1}: "${book.title}"`);
      console.log(`   ID: ${book.id}`);
      console.log(`   Owner: ${book.ownerId}`);
      console.log(`   Chapters: ${book.chapters?.length || 0}`);
      console.log(`   Total Scenes: ${book.chapters?.reduce((total, ch) => total + (ch.scenes?.length || 0), 0) || 0}`);
      console.log(`   Word Count: ${book.wordCount || 0}`);
      console.log(`   Characters: ${book.characters?.length || 0}`);
      console.log(`   Plot Points: ${book.plotPoints?.length || 0}`);

      if (book.chapters && book.chapters.length > 0) {
        console.log(`   📝 Chapter Details:`);
        book.chapters.forEach((chapter, chIndex) => {
          console.log(`     ${chIndex + 1}. "${chapter.title}" - ${chapter.scenes?.length || 0} scenes`);
          if (chapter.scenes && chapter.scenes.length > 0) {
            chapter.scenes.forEach((scene, sIndex) => {
              console.log(`        ${sIndex + 1}. "${scene.title}" - ${scene.wordCount || 0} words`);
              if (scene.content) {
                const preview = scene.content.substring(0, 100) + (scene.content.length > 100 ? '...' : '');
                console.log(`           Preview: "${preview}"`);
              }
            });
          }
        });
      }
    });

    if (books.length === 0) {
      console.log('❌ No collaborative books found. Make sure:');
      console.log('   1. You have been added as a collaborator to at least one book');
      console.log('   2. The migration has been run: collaborationMigration.runAll()');
      console.log('   3. The book owner has added you with the correct email/user ID');
    }

  } catch (error) {
    console.error('❌ Error testing collaborative book data:', error);
  }
}

// Make debug functions available globally
declare global {
  interface Window {
    debugCollaboration: {
      debugCollaborativeBooks: typeof debugCollaborativeBooks;
      debugBookCollaboration: typeof debugBookCollaboration;
      debugUserLookup: typeof debugUserLookup;
      debugAll: typeof debugAllCollaboration;
      testAddCollaborator: typeof testAddCollaborator;
      testCollaborativeBookData: typeof testCollaborativeBookData;
    };
  }
}

// Expose debug utilities globally
if (typeof window !== 'undefined') {
  window.debugCollaboration = {
    debugCollaborativeBooks,
    debugBookCollaboration,
    debugUserLookup,
    debugAll: debugAllCollaboration,
    testAddCollaborator,
    testCollaborativeBookData
  };

  console.log('🔧 Collaboration debug utilities available:');
  console.log('- debugCollaboration.debugAll(userId) - Full debug');
  console.log('- debugCollaboration.debugCollaborativeBooks(userId) - Check collaborative books');
  console.log('- debugCollaboration.debugBookCollaboration(bookId) - Check specific book');
  console.log('- debugCollaboration.debugUserLookup() - Check user lookup table');
  console.log('- debugCollaboration.testAddCollaborator(bookId, email, userId) - Test adding collaborator');
  console.log('- debugCollaboration.testCollaborativeBookData(userId) - Test loading book data');
}
