import { useState, useEffect } from 'react';

export const useImageBrightness = (imageUrl: string) => {
  const [isDark, setIsDark] = useState(true);

  useEffect(() => {
    const analyzeImage = () => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        const imageData = ctx?.getImageData(0, 0, canvas.width, canvas.height);
        if (!imageData) return;
        
        let totalBrightness = 0;
        const pixels = imageData.data;
        
        for (let i = 0; i < pixels.length; i += 4) {
          const r = pixels[i];
          const g = pixels[i + 1];
          const b = pixels[i + 2];
          const brightness = (r * 0.299 + g * 0.587 + b * 0.114);
          totalBrightness += brightness;
        }
        
        const avgBrightness = totalBrightness / (pixels.length / 4);
        setIsDark(avgBrightness < 128);
      };
      
      img.onerror = () => setIsDark(true);
      img.src = imageUrl;
    };

    analyzeImage();
  }, [imageUrl]);

  return isDark;
};