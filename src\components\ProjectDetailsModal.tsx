import React, { useState } from 'react';
import { X, ArrowLeft } from 'lucide-react';
import { ProjectType } from '../types';

interface ProjectDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onCreateProject: (title: string, author: string, projectType: ProjectType) => void;
  projectType: ProjectType;
  backgroundImage: string;
}

const getProjectTypeDisplayName = (type: ProjectType): string => {
  const typeMap: Record<ProjectType, string> = {
    'novel': 'Novel',
    'novella': 'Novella',
    'short-story': 'Short Story',
    'poem': 'Poetry Collection',
    'screenplay': 'Screenplay',
    'essay': 'Essay Collection',
    'journal': 'Journal/Memoir'
  };
  return typeMap[type];
};

const getProjectTypePlaceholder = (type: ProjectType): { title: string; author: string } => {
  const placeholders: Record<ProjectType, { title: string; author: string }> = {
    'novel': { title: 'The Great Adventure', author: 'Your Name' },
    'novella': { title: 'A Brief Tale', author: 'Your Name' },
    'short-story': { title: 'The Moment', author: 'Your Name' },
    'poem': { title: 'Reflections in Verse', author: 'Your Name' },
    'screenplay': { title: 'The Script', author: 'Your Name' },
    'essay': { title: 'Thoughts and Ideas', author: 'Your Name' },
    'journal': { title: 'My Journey', author: 'Your Name' }
  };
  return placeholders[type];
};

export const ProjectDetailsModal: React.FC<ProjectDetailsModalProps> = ({
  isOpen,
  onClose,
  onBack,
  onCreateProject,
  projectType,
  backgroundImage
}) => {
  const [title, setTitle] = useState('');
  const [author, setAuthor] = useState('');

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !author.trim()) return;
    
    onCreateProject(title.trim(), author.trim(), projectType);
    setTitle('');
    setAuthor('');
  };

  const placeholders = getProjectTypePlaceholder(projectType);
  const displayName = getProjectTypeDisplayName(projectType);

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div 
        className="bg-black/70 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl max-w-md w-full"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/30">
          <div className="flex items-center">
            <button
              onClick={onBack}
              className="p-2 hover:bg-black/40 rounded-lg transition-colors text-white mr-3 bg-black/20 backdrop-blur-sm"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h2 className="text-xl font-serif font-bold text-white">Create {displayName}</h2>
              <p className="text-white/90 font-sans text-sm mt-1">Enter your project details</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-black/40 rounded-lg transition-colors text-white bg-black/20 backdrop-blur-sm"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className="block text-white font-sans font-medium mb-2">
              {projectType === 'poem' || projectType === 'essay' ? 'Collection Title' : 'Title'}
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder={placeholders.title}
              className="w-full px-4 py-3 bg-black/50 border border-white/30 rounded-lg font-sans text-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent backdrop-blur-sm"
              autoFocus
            />
          </div>

          <div>
            <label className="block text-white font-sans font-medium mb-2">
              Author
            </label>
            <input
              type="text"
              value={author}
              onChange={(e) => setAuthor(e.target.value)}
              placeholder={placeholders.author}
              className="w-full px-4 py-3 bg-black/50 border border-white/30 rounded-lg font-sans text-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent backdrop-blur-sm"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              disabled={!title.trim() || !author.trim()}
              className="flex-1 px-6 py-3 bg-black/50 backdrop-blur-md hover:bg-black/60 disabled:bg-black/30 disabled:cursor-not-allowed text-white font-sans font-medium rounded-lg border border-white/30 transition-colors"
            >
              Create {displayName}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-3 bg-black/40 backdrop-blur-md hover:bg-black/50 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
