import { 
  collection, 
  query, 
  where, 
  getDocs, 
  updateDoc,
  doc,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import { ShareTokenService } from './shareTokenService';
import { ShareActivityService } from './shareActivityService';
import type { SharedDocument } from '../types/sharing';

/**
 * Service for cleaning up expired shares and maintaining data hygiene
 */
export class ShareCleanupService {
  
  /**
   * Mark expired shares as expired
   */
  static async markExpiredShares(): Promise<{
    success: boolean;
    expiredCount?: number;
    error?: string;
  }> {
    try {
      const now = Timestamp.now();
      
      // Find shares that have expired but are not marked as expired
      const q = query(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS),
        where('expiresAt', '<', now),
        where('status', 'in', ['pending', 'accepted'])
      );

      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return { success: true, expiredCount: 0 };
      }

      // Update expired shares
      const updatePromises = querySnapshot.docs.map(async (docSnapshot) => {
        const shareData = docSnapshot.data() as SharedDocument;
        
        await updateDoc(doc(db, COLLECTIONS.SHARED_DOCUMENTS, docSnapshot.id), {
          status: 'expired',
          updatedAt: now
        });

        // Log the expiration activity
        await ShareActivityService.logActivity(
          docSnapshot.id,
          'system',
          'System',
          'revoked',
          'Share expired automatically',
          { ipAddress: null, userAgent: 'ShareCleanupService' }
        );
      });

      await Promise.all(updatePromises);

      return {
        success: true,
        expiredCount: querySnapshot.docs.length
      };
    } catch (error) {
      console.error('Error marking expired shares:', error);
      return {
        success: false,
        error: 'Failed to mark expired shares'
      };
    }
  }

  /**
   * Clean up expired share tokens
   */
  static async cleanupExpiredTokens(): Promise<{
    success: boolean;
    deletedCount?: number;
    error?: string;
  }> {
    try {
      return await ShareTokenService.cleanupExpiredTokens();
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
      return {
        success: false,
        error: 'Failed to cleanup expired tokens'
      };
    }
  }

  /**
   * Get cleanup statistics
   */
  static async getCleanupStats(): Promise<{
    totalShares: number;
    activeShares: number;
    expiredShares: number;
    pendingShares: number;
    completedShares: number;
    revokedShares: number;
  }> {
    try {
      // Get all shares
      const allSharesQuery = query(collection(db, COLLECTIONS.SHARED_DOCUMENTS));
      const allSharesSnapshot = await getDocs(allSharesQuery);
      
      const stats = {
        totalShares: allSharesSnapshot.docs.length,
        activeShares: 0,
        expiredShares: 0,
        pendingShares: 0,
        completedShares: 0,
        revokedShares: 0
      };

      allSharesSnapshot.docs.forEach(doc => {
        const share = doc.data() as SharedDocument;
        
        switch (share.status) {
          case 'pending':
            stats.pendingShares++;
            break;
          case 'accepted':
            stats.activeShares++;
            break;
          case 'completed':
            stats.completedShares++;
            break;
          case 'expired':
            stats.expiredShares++;
            break;
          case 'revoked':
            stats.revokedShares++;
            break;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error getting cleanup stats:', error);
      return {
        totalShares: 0,
        activeShares: 0,
        expiredShares: 0,
        pendingShares: 0,
        completedShares: 0,
        revokedShares: 0
      };
    }
  }

  /**
   * Run full cleanup process
   */
  static async runFullCleanup(): Promise<{
    success: boolean;
    results: {
      expiredShares: { success: boolean; expiredCount?: number; error?: string };
      expiredTokens: { success: boolean; deletedCount?: number; error?: string };
    };
    error?: string;
  }> {
    try {
      console.log('🧹 Starting share cleanup process...');

      // Mark expired shares
      const expiredSharesResult = await this.markExpiredShares();
      console.log(`📊 Expired shares: ${expiredSharesResult.expiredCount || 0}`);

      // Clean up expired tokens
      const expiredTokensResult = await this.cleanupExpiredTokens();
      console.log(`🗑️ Deleted tokens: ${expiredTokensResult.deletedCount || 0}`);

      const allSuccessful = expiredSharesResult.success && expiredTokensResult.success;

      return {
        success: allSuccessful,
        results: {
          expiredShares: expiredSharesResult,
          expiredTokens: expiredTokensResult
        }
      };
    } catch (error) {
      console.error('Error running full cleanup:', error);
      return {
        success: false,
        results: {
          expiredShares: { success: false, error: 'Failed to process' },
          expiredTokens: { success: false, error: 'Failed to process' }
        },
        error: 'Failed to run full cleanup'
      };
    }
  }

  /**
   * Schedule periodic cleanup (call this on app initialization)
   */
  static schedulePeriodicCleanup(intervalHours: number = 24): void {
    const intervalMs = intervalHours * 60 * 60 * 1000;
    
    // Run initial cleanup
    this.runFullCleanup();
    
    // Schedule periodic cleanup
    setInterval(() => {
      this.runFullCleanup();
    }, intervalMs);
    
    console.log(`🕒 Scheduled share cleanup every ${intervalHours} hours`);
  }

  /**
   * Validate share access (check if share is still valid)
   */
  static async validateShareAccess(shareId: string): Promise<{
    isValid: boolean;
    reason?: string;
    share?: SharedDocument;
  }> {
    try {
      const shareDoc = await getDocs(
        query(
          collection(db, COLLECTIONS.SHARED_DOCUMENTS),
          where('__name__', '==', shareId)
        )
      );

      if (shareDoc.empty) {
        return {
          isValid: false,
          reason: 'Share not found'
        };
      }

      const share = shareDoc.docs[0].data() as SharedDocument;

      // Check if expired
      if (share.expiresAt && share.expiresAt.toDate() < new Date()) {
        return {
          isValid: false,
          reason: 'Share has expired',
          share
        };
      }

      // Check if revoked
      if (share.status === 'revoked') {
        return {
          isValid: false,
          reason: 'Share has been revoked',
          share
        };
      }

      // Check if declined
      if (share.status === 'declined') {
        return {
          isValid: false,
          reason: 'Share was declined',
          share
        };
      }

      return {
        isValid: true,
        share
      };
    } catch (error) {
      console.error('Error validating share access:', error);
      return {
        isValid: false,
        reason: 'Failed to validate share access'
      };
    }
  }

  /**
   * Send reminder notifications for pending shares (would integrate with email service)
   */
  static async sendPendingShareReminders(): Promise<{
    success: boolean;
    remindersSent?: number;
    error?: string;
  }> {
    try {
      // Find shares that are pending for more than 3 days
      const threeDaysAgo = Timestamp.fromDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000));
      
      const q = query(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS),
        where('status', '==', 'pending'),
        where('createdAt', '<', threeDaysAgo)
      );

      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return { success: true, remindersSent: 0 };
      }

      // In a real implementation, you would send email reminders here
      console.log(`📧 Would send ${querySnapshot.docs.length} reminder emails`);

      return {
        success: true,
        remindersSent: querySnapshot.docs.length
      };
    } catch (error) {
      console.error('Error sending reminder notifications:', error);
      return {
        success: false,
        error: 'Failed to send reminder notifications'
      };
    }
  }
}
