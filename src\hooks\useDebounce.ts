import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Custom hook for debouncing values
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Custom hook for debounced callbacks
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callbackRef.current(...args);
      }, delay);
    },
    [delay]
  ) as T;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
}

/**
 * Custom hook for autosave functionality with debouncing
 */
export function useAutosave<T>(
  value: T,
  saveFunction: (value: T) => Promise<void> | void,
  delay: number = 2000,
  enabled: boolean = true
) {
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const saveRef = useRef(saveFunction);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastValueRef = useRef<T>(value);

  // Update save function ref
  useEffect(() => {
    saveRef.current = saveFunction;
  }, [saveFunction]);

  // Track unsaved changes
  useEffect(() => {
    if (JSON.stringify(value) !== JSON.stringify(lastValueRef.current)) {
      setHasUnsavedChanges(true);
    }
  }, [value]);

  // Debounced save effect
  useEffect(() => {
    if (!enabled) return;

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(async () => {
      if (JSON.stringify(value) !== JSON.stringify(lastValueRef.current)) {
        setIsSaving(true);
        try {
          await saveRef.current(value);
          setLastSaved(new Date());
          setHasUnsavedChanges(false);
          lastValueRef.current = value;
        } catch (error) {
          console.error('Autosave failed:', error);
        } finally {
          setIsSaving(false);
        }
      }
    }, delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, delay, enabled]);

  // Manual save function
  const saveNow = useCallback(async () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (JSON.stringify(value) !== JSON.stringify(lastValueRef.current)) {
      setIsSaving(true);
      try {
        await saveRef.current(value);
        setLastSaved(new Date());
        setHasUnsavedChanges(false);
        lastValueRef.current = value;
      } catch (error) {
        console.error('Manual save failed:', error);
        throw error;
      } finally {
        setIsSaving(false);
      }
    }
  }, [value]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    isSaving,
    lastSaved,
    hasUnsavedChanges,
    saveNow
  };
}
