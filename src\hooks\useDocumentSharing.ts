import { useState, useCallback } from 'react';
import {
  collection,
  addDoc,
  query,
  where,
  getDocs,
  doc,
  updateDoc,
  deleteDoc,
  Timestamp,
  getDoc,
  setDoc,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import { Book, Chapter, Scene } from '../types';
import { exportBookToPDF } from '../utils/pdfExport';
import { UserLookupService } from '../services/userLookupService';
import { EmailNotificationService } from '../services/emailNotificationService';
import { ShareTokenService } from '../services/shareTokenService';
import { ShareActivityService } from '../services/shareActivityService';
import { NotificationService } from '../services/notificationService';
import type { SharedDocument, SharePermission, ShareStatus } from '../types/sharing';

interface UseDocumentSharingProps {
  userId: string; // Firebase UID
  userName: string;
  userEmail: string;
}

export const useDocumentSharing = ({ userId, userName, userEmail }: UseDocumentSharingProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Share a document with another user
  const shareDocument = useCallback(async (
    book: Book,
    recipientEmail: string,
    permission: SharePermission,
    message: string | null = null,
    expirationDays: number = 30
  ) => {
    setLoading(true);
    setError(null);

    try {
      // Validate inputs
      if (!book || !book.id) {
        throw new Error('Invalid book data');
      }

      // Validate and find recipient
      const recipientValidation = await UserLookupService.validateRecipientEmail(recipientEmail);
      if (!recipientValidation.isValid) {
        const errorMsg = recipientValidation.error || 'Invalid recipient';
        console.error('❌ Recipient validation failed:', errorMsg);

        // Provide helpful error message with instructions
        if (errorMsg.includes('User not found')) {
          throw new Error(
            `User not found: ${recipientEmail}\n\n` +
            `The recipient needs to:\n` +
            `1. Create an account on WriterOne\n` +
            `2. Log in at least once\n\n` +
            `Or you can manually add them using the browser console:\n` +
            `userSyncUtils.manuallyAddUser("${recipientEmail}", "Display Name")`
          );
        }

        throw new Error(errorMsg);
      }

      const recipient = recipientValidation.user!;

      // Check if user is trying to share with themselves
      if (recipient.uid === userId) {
        throw new Error('You cannot share a document with yourself');
      }

      // Check if document is already shared with this user
      const existingShareQuery = query(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS),
        where('bookId', '==', book.id),
        where('senderId', '==', userId),
        where('recipientId', '==', recipient.uid),
        where('status', 'in', ['pending', 'accepted'])
      );

      const existingShares = await getDocs(existingShareQuery);
      if (!existingShares.empty) {
        throw new Error('This document is already shared with this user');
      }

      // Create a shared document record
      const sharedDocData: Omit<SharedDocument, 'id'> = {
        bookId: book.id,
        bookTitle: book.title,
        senderId: userId,
        senderName: userName,
        senderEmail: userEmail,
        recipientId: recipient.uid,
        recipientEmail: recipient.email,
        recipientName: recipient.displayName,
        permission,
        status: 'pending',
        message,
        returnMessage: null,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        expiresAt: Timestamp.fromDate(new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000)),
        acceptedAt: null,
        completedAt: null,
        revokedAt: null,
        lastAccessedAt: null,
        accessCount: 0
      };

      const sharedDocRef = await addDoc(collection(db, COLLECTIONS.SHARED_DOCUMENTS), sharedDocData);

      // Create a copy of the book for the shared document
      const bookCopy = {
        ...book,
        sharedId: sharedDocRef.id,
        originalBookId: book.id,
        sharedBy: userName,
        sharedAt: Timestamp.now()
      };

      // Store the book copy in a subcollection
      await setDoc(doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocRef.id, 'bookData', 'book'), bookCopy);

      // Create secure share token and invitation
      const tokenResult = await ShareTokenService.createShareInvitation(
        sharedDocRef.id,
        recipient.email,
        expirationDays * 24
      );

      if (!tokenResult.success) {
        console.error('Failed to create share token:', tokenResult.error);
        // Continue without token - fallback to direct access
      }

      // Log activity
      await ShareActivityService.logShareCreated(
        sharedDocRef.id,
        userId,
        userName,
        recipient.email
      );

      // Send email notification
      try {
        await EmailNotificationService.sendShareNotification({
          type: 'share_created',
          recipientEmail: recipient.email,
          senderName: userName,
          bookTitle: book.title,
          shareUrl: tokenResult.shareUrl || `${window.location.origin}/shared/${sharedDocRef.id}`,
          message,
          expiresAt: sharedDocData.expiresAt
        });
      } catch (emailError) {
        console.error('Failed to send email notification:', emailError);
        // Don't fail the whole operation if email fails
      }

      // Create in-app notification for recipient
      try {
        await NotificationService.notifyShareReceived(
          recipient.uid,
          userName,
          book.title,
          sharedDocRef.id,
          tokenResult.shareUrl
        );
        console.log('✅ In-app notification created for recipient');
      } catch (notificationError) {
        console.error('Failed to create in-app notification:', notificationError);
        // Don't fail the whole operation if notification fails
      }

      return {
        success: true,
        sharedDocumentId: sharedDocRef.id,
        shareUrl: tokenResult.shareUrl
      };
    } catch (err: any) {
      console.error('Error sharing document:', err);
      const errorMessage = err.message || 'Failed to share document. Please try again.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [userId, userName, userEmail]);

  // Get documents shared with the current user
  const getSharedWithMe = useCallback(async (statusFilter?: ShareStatus[]) => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Getting shared with me for userId:', userId);

      // Simple query first to avoid index issues
      const q = query(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS),
        where('recipientId', '==', userId)
      );

      const querySnapshot = await getDocs(q);
      console.log('📊 Found', querySnapshot.docs.length, 'documents shared with me');

      let sharedDocs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as SharedDocument));

      // Filter by status in memory if needed
      if (statusFilter && statusFilter.length > 0) {
        sharedDocs = sharedDocs.filter(doc => statusFilter.includes(doc.status));
      }

      // Sort by creation date (newest first)
      sharedDocs.sort((a, b) => {
        const aTime = a.createdAt?.toMillis() || 0;
        const bTime = b.createdAt?.toMillis() || 0;
        return bTime - aTime;
      });

      console.log('✅ Returning', sharedDocs.length, 'filtered documents');
      return sharedDocs;
    } catch (err) {
      console.error('❌ Error getting shared documents:', err);
      setError('Failed to retrieve shared documents.');
      return [];
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Get documents shared by the current user
  const getSharedByMe = useCallback(async (statusFilter?: ShareStatus[]) => {
    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Getting shared by me for userId:', userId);

      // Simple query first to avoid index issues
      const q = query(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS),
        where('senderId', '==', userId)
      );

      const querySnapshot = await getDocs(q);
      console.log('📊 Found', querySnapshot.docs.length, 'documents shared by me');

      let sharedDocs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as SharedDocument));

      // Filter by status in memory if needed
      if (statusFilter && statusFilter.length > 0) {
        sharedDocs = sharedDocs.filter(doc => statusFilter.includes(doc.status));
      }

      // Sort by creation date (newest first)
      sharedDocs.sort((a, b) => {
        const aTime = a.createdAt?.toMillis() || 0;
        const bTime = b.createdAt?.toMillis() || 0;
        return bTime - aTime;
      });

      console.log('✅ Returning', sharedDocs.length, 'filtered documents');
      return sharedDocs;
    } catch (err) {
      console.error('❌ Error getting shared documents:', err);
      setError('Failed to retrieve shared documents.');
      return [];
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Accept a shared document
  const acceptSharedDocument = useCallback(async (sharedDocumentId: string, token?: string) => {
    setLoading(true);
    setError(null);

    try {
      // Validate token if provided
      if (token) {
        const tokenValidation = await ShareTokenService.validateShareToken(sharedDocumentId, token);
        if (!tokenValidation.isValid) {
          throw new Error(tokenValidation.error || 'Invalid or expired share link');
        }
      }

      const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        throw new Error('Shared document not found');
      }

      const sharedDoc = docSnap.data() as SharedDocument;

      // Verify user has permission to accept
      if (sharedDoc.recipientId !== userId) {
        throw new Error('You do not have permission to accept this document');
      }

      // Check if already accepted
      if (sharedDoc.status === 'accepted') {
        return { success: true, message: 'Document already accepted' };
      }

      // Check if expired
      if (sharedDoc.expiresAt && sharedDoc.expiresAt.toDate() < new Date()) {
        throw new Error('This share has expired');
      }

      await updateDoc(docRef, {
        status: 'accepted',
        acceptedAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      });

      // Mark token as used if provided
      if (token) {
        await ShareTokenService.markTokenAsUsed(sharedDocumentId, token);
      }

      // Log activity
      await ShareActivityService.logShareAccepted(sharedDocumentId, userId, userName);

      // Send notification to sender
      try {
        await EmailNotificationService.sendShareNotification({
          type: 'share_accepted',
          recipientEmail: sharedDoc.senderEmail,
          senderName: userName,
          bookTitle: sharedDoc.bookTitle,
          shareUrl: `${window.location.origin}/shared/${sharedDocumentId}`
        });
      } catch (emailError) {
        console.error('Failed to send acceptance notification:', emailError);
      }

      // Create in-app notification for sender
      try {
        await NotificationService.notifyShareAccepted(
          sharedDoc.senderId,
          userName,
          sharedDoc.bookTitle,
          sharedDocumentId
        );
        console.log('✅ In-app notification created for sender');
      } catch (notificationError) {
        console.error('Failed to create acceptance notification:', notificationError);
      }

      return { success: true };
    } catch (err: any) {
      console.error('Error accepting shared document:', err);
      const errorMessage = err.message || 'Failed to accept document.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [userId, userName]);

  // Get a shared document by ID
  const getSharedDocument = useCallback(async (sharedDocumentId: string, token?: string) => {
    setLoading(true);
    setError(null);

    try {
      // Validate token if provided
      if (token) {
        const tokenValidation = await ShareTokenService.validateShareToken(sharedDocumentId, token);
        if (!tokenValidation.isValid) {
          throw new Error(tokenValidation.error || 'Invalid or expired share link');
        }
      }

      // Get the shared document metadata
      const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        throw new Error('Shared document not found');
      }

      const sharedDoc = docSnap.data() as SharedDocument;

      // Check permissions
      const isSender = sharedDoc.senderId === userId;
      const isRecipient = sharedDoc.recipientId === userId;

      if (!isSender && !isRecipient && !token) {
        throw new Error('You do not have permission to access this document');
      }

      // Check if expired
      if (sharedDoc.expiresAt && sharedDoc.expiresAt.toDate() < new Date()) {
        throw new Error('This share has expired');
      }

      // Get the book data from the subcollection
      const bookRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, 'bookData', 'book');
      const bookSnap = await getDoc(bookRef);

      if (!bookSnap.exists()) {
        throw new Error('Shared book data not found');
      }

      // Update access tracking
      await updateDoc(docRef, {
        lastAccessedAt: Timestamp.now(),
        accessCount: (sharedDoc.accessCount || 0) + 1
      });

      // Log activity
      await ShareActivityService.logShareViewed(sharedDocumentId, userId, userName);

      return {
        metadata: { id: docSnap.id, ...sharedDoc },
        book: bookSnap.data()
      };
    } catch (err: any) {
      console.error('Error getting shared document:', err);
      const errorMessage = err.message || 'Failed to retrieve shared document.';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [userId, userName]);

  // Add a comment to a shared document
  const addComment = useCallback(async (
    sharedDocumentId: string,
    content: string,
    position: {
      chapterId: string;
      sceneId: string;
      textPosition: number;
    } | null = null
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const commentRef = await addDoc(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, COLLECTIONS.COMMENTS),
        {
          sharedDocumentId,
          userId,
          userName,
          content,
          position,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        }
      );
      
      return { success: true, commentId: commentRef.id };
    } catch (err) {
      console.error('Error adding comment:', err);
      setError('Failed to add comment.');
      return { success: false, error: err };
    } finally {
      setLoading(false);
    }
  }, [userId, userName]);

  // Get comments for a shared document
  const getComments = useCallback(async (sharedDocumentId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const commentsRef = collection(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, COLLECTIONS.COMMENTS);
      const querySnapshot = await getDocs(commentsRef);
      
      const comments = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      return comments;
    } catch (err) {
      console.error('Error getting comments:', err);
      setError('Failed to retrieve comments.');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Update a shared document (for editors)
  const updateSharedDocument = useCallback(async (
    sharedDocumentId: string,
    updatedBook: Book
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      // Update the book data in the subcollection
      const bookRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, 'bookData', 'book');
      await updateDoc(bookRef, {
        ...updatedBook,
        updatedAt: Timestamp.now()
      });
      
      return { success: true };
    } catch (err) {
      console.error('Error updating shared document:', err);
      setError('Failed to update document.');
      return { success: false, error: err };
    } finally {
      setLoading(false);
    }
  }, []);

  // Complete and return a shared document
  const completeSharedDocument = useCallback(async (
    sharedDocumentId: string,
    book: Book,
    returnMessage: string | null = null
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      // Validate inputs
      if (!sharedDocumentId) {
        throw new Error('Invalid shared document ID');
      }
      
      if (!book || !book.id) {
        throw new Error('Invalid book data');
      }
      
      // Check if document exists
      const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        throw new Error('Shared document not found');
      }
      
      // Update the shared document status
      await updateDoc(docRef, {
        status: 'completed',
        updatedAt: Timestamp.now(),
        returnMessage: returnMessage
      });
      
      // Update the book data
      const bookRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, 'bookData', 'book');
      await updateDoc(bookRef, {
        ...book,
        updatedAt: Timestamp.now()
      });
      
      return { success: true };
    } catch (err: any) {
      console.error('Error completing shared document:', err);
      const errorMessage = err.message || 'Failed to complete document.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  // Export shared document to PDF
  const exportSharedDocumentToPDF = useCallback((book: Book) => {
    try {
      exportBookToPDF(book);
      return { success: true };
    } catch (err) {
      console.error('Error exporting to PDF:', err);
      setError('Failed to export document to PDF.');
      return { success: false, error: err };
    }
  }, []);

  // Check for returned documents
  const checkForReturnedDocuments = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS),
        where('senderId', '==', userId),
        where('status', '==', 'completed')
      );
      
      const querySnapshot = await getDocs(q);
      const returnedDocs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      return returnedDocs;
    } catch (err) {
      console.error('Error checking for returned documents:', err);
      setError('Failed to check for returned documents.');
      return [];
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Accept edits from a returned document
  const acceptEdits = useCallback(async (sharedDocumentId: string, originalBookId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Get the shared document data
      const sharedDoc = await getSharedDocument(sharedDocumentId);
      
      if (!sharedDoc || !sharedDoc.book) {
        throw new Error('Shared document not found');
      }
      
      // Get the original book
      const originalBookRef = doc(db, COLLECTIONS.BOOKS, originalBookId);
      const originalBookSnap = await getDoc(originalBookRef);
      
      if (!originalBookSnap.exists()) {
        throw new Error('Original book not found');
      }
      
      // Update the original book with the edited content
      await updateDoc(originalBookRef, {
        chapters: sharedDoc.book.chapters,
        updatedAt: Timestamp.now()
      });
      
      // Get comments from the shared document
      const commentsRef = collection(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, COLLECTIONS.COMMENTS);
      const commentsSnap = await getDocs(commentsRef);
      const comments = commentsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
      // Create a subcollection for comments in the original book
      const bookCommentsRef = collection(db, COLLECTIONS.BOOKS, originalBookId, 'editorComments');
      
      // Add all comments to the original book
      for (const comment of comments) {
        await addDoc(bookCommentsRef, {
          ...comment,
          sharedDocumentId,
          importedAt: Timestamp.now()
        });
      }
      
      // Mark the shared document as synced
      const sharedDocRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      await updateDoc(sharedDocRef, {
        syncedWithOriginal: true,
        syncedAt: Timestamp.now(),
        status: 'accepted'
      });
      
      return { success: true };
    } catch (err: any) {
      console.error('Error accepting edits:', err);
      const errorMessage = err.message || 'Failed to accept edits.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [getSharedDocument]);
  
  // Decline edits from a returned document
  const declineEdits = useCallback(async (sharedDocumentId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Mark the shared document as declined
      const sharedDocRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      await updateDoc(sharedDocRef, {
        status: 'declined',
        declinedAt: Timestamp.now()
      });
      
      return { success: true };
    } catch (err: any) {
      console.error('Error declining edits:', err);
      const errorMessage = err.message || 'Failed to decline edits.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  // Revoke access to a shared document
  const revokeSharedDocument = useCallback(async (sharedDocumentId: string, reason?: string) => {
    setLoading(true);
    setError(null);

    try {
      const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        throw new Error('Shared document not found');
      }

      const sharedDoc = docSnap.data() as SharedDocument;

      // Only sender can revoke
      if (sharedDoc.senderId !== userId) {
        throw new Error('You do not have permission to revoke this share');
      }

      await updateDoc(docRef, {
        status: 'revoked',
        revokedAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        returnMessage: reason || 'Access revoked by sender'
      });

      // Log activity
      await ShareActivityService.logShareRevoked(sharedDocumentId, userId, userName, reason);

      return { success: true };
    } catch (err: any) {
      console.error('Error revoking shared document:', err);
      const errorMessage = err.message || 'Failed to revoke document access.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [userId, userName]);

  // Decline a shared document
  const declineSharedDocument = useCallback(async (sharedDocumentId: string, reason?: string) => {
    setLoading(true);
    setError(null);

    try {
      const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        throw new Error('Shared document not found');
      }

      const sharedDoc = docSnap.data() as SharedDocument;

      // Only recipient can decline
      if (sharedDoc.recipientId !== userId) {
        throw new Error('You do not have permission to decline this share');
      }

      await updateDoc(docRef, {
        status: 'declined',
        updatedAt: Timestamp.now(),
        returnMessage: reason || 'Share declined by recipient'
      });

      // Log activity
      await ShareActivityService.logShareDeclined(sharedDocumentId, userId, userName, reason);

      return { success: true };
    } catch (err: any) {
      console.error('Error declining shared document:', err);
      const errorMessage = err.message || 'Failed to decline document.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [userId, userName]);

  return {
    loading,
    error,
    shareDocument,
    getSharedWithMe,
    getSharedByMe,
    acceptSharedDocument,
    getSharedDocument,
    addComment,
    getComments,
    updateSharedDocument,
    completeSharedDocument,
    exportSharedDocumentToPDF,
    checkForReturnedDocuments,
    acceptEdits,
    declineEdits,
    revokeSharedDocument,
    declineSharedDocument
  };
};