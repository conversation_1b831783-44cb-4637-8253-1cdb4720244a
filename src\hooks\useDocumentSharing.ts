import { useState, useCallback } from 'react';
import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc, 
  Timestamp,
  getDoc,
  setDoc
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import { Book, Chapter, Scene } from '../types';
import { exportBookToPDF } from '../utils/pdfExport';

interface UseDocumentSharingProps {
  userId: string;
  userName: string;
}

export const useDocumentSharing = ({ userId, userName }: UseDocumentSharingProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Share a document with another user
  const shareDocument = useCallback(async (
    book: Book,
    recipientEmail: string,
    permission: 'view' | 'edit',
    message: string | null = null
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      // Validate inputs
      if (!book || !book.id) {
        throw new Error('Invalid book data');
      }
      
      if (!recipientEmail || !recipientEmail.includes('@')) {
        throw new Error('Invalid recipient email');
      }
      
      // Create a shared document record
      const sharedDocRef = await addDoc(collection(db, COLLECTIONS.SHARED_DOCUMENTS), {
        bookId: book.id,
        bookTitle: book.title, // Store book title for easier listing
        senderId: userId,
        senderName: userName,
        recipientEmail: recipientEmail.toLowerCase(),
        permission,
        status: 'pending',
        message,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        expiresAt: Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) // 30 days from now
      });

      // Create a copy of the book for the shared document
      const bookCopy = {
        ...book,
        sharedId: sharedDocRef.id,
        originalBookId: book.id,
        sharedBy: userName,
        sharedAt: Timestamp.now()
      };

      // Store the book copy in a subcollection
      await setDoc(doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocRef.id, 'bookData', 'book'), bookCopy);

      return { success: true, sharedDocumentId: sharedDocRef.id };
    } catch (err: any) {
      console.error('Error sharing document:', err);
      const errorMessage = err.message || 'Failed to share document. Please try again.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [userId, userName]);

  // Get documents shared with the current user
  const getSharedWithMe = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS),
        where('recipientEmail', '==', userId) // Using userId as email for simplicity
      );
      
      const querySnapshot = await getDocs(q);
      const sharedDocs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      return sharedDocs;
    } catch (err) {
      console.error('Error getting shared documents:', err);
      setError('Failed to retrieve shared documents.');
      return [];
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Get documents shared by the current user
  const getSharedByMe = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS),
        where('senderId', '==', userId)
      );
      
      const querySnapshot = await getDocs(q);
      const sharedDocs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      return sharedDocs;
    } catch (err) {
      console.error('Error getting shared documents:', err);
      setError('Failed to retrieve shared documents.');
      return [];
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Accept a shared document
  const acceptSharedDocument = useCallback(async (sharedDocumentId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      await updateDoc(docRef, {
        status: 'accepted',
        updatedAt: Timestamp.now()
      });
      
      return { success: true };
    } catch (err) {
      console.error('Error accepting shared document:', err);
      setError('Failed to accept document.');
      return { success: false, error: err };
    } finally {
      setLoading(false);
    }
  }, []);

  // Get a shared document by ID
  const getSharedDocument = useCallback(async (sharedDocumentId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Get the shared document metadata
      const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        setError('Shared document not found.');
        return null;
      }
      
      // Get the book data from the subcollection
      const bookRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, 'bookData', 'book');
      const bookSnap = await getDoc(bookRef);
      
      if (!bookSnap.exists()) {
        setError('Shared book data not found.');
        return null;
      }
      
      return {
        metadata: { id: docSnap.id, ...docSnap.data() },
        book: bookSnap.data()
      };
    } catch (err) {
      console.error('Error getting shared document:', err);
      setError('Failed to retrieve shared document.');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Add a comment to a shared document
  const addComment = useCallback(async (
    sharedDocumentId: string,
    content: string,
    position: {
      chapterId: string;
      sceneId: string;
      textPosition: number;
    } | null = null
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const commentRef = await addDoc(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, COLLECTIONS.COMMENTS),
        {
          sharedDocumentId,
          userId,
          userName,
          content,
          position,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        }
      );
      
      return { success: true, commentId: commentRef.id };
    } catch (err) {
      console.error('Error adding comment:', err);
      setError('Failed to add comment.');
      return { success: false, error: err };
    } finally {
      setLoading(false);
    }
  }, [userId, userName]);

  // Get comments for a shared document
  const getComments = useCallback(async (sharedDocumentId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const commentsRef = collection(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, COLLECTIONS.COMMENTS);
      const querySnapshot = await getDocs(commentsRef);
      
      const comments = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      return comments;
    } catch (err) {
      console.error('Error getting comments:', err);
      setError('Failed to retrieve comments.');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Update a shared document (for editors)
  const updateSharedDocument = useCallback(async (
    sharedDocumentId: string,
    updatedBook: Book
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      // Update the book data in the subcollection
      const bookRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, 'bookData', 'book');
      await updateDoc(bookRef, {
        ...updatedBook,
        updatedAt: Timestamp.now()
      });
      
      return { success: true };
    } catch (err) {
      console.error('Error updating shared document:', err);
      setError('Failed to update document.');
      return { success: false, error: err };
    } finally {
      setLoading(false);
    }
  }, []);

  // Complete and return a shared document
  const completeSharedDocument = useCallback(async (
    sharedDocumentId: string,
    book: Book,
    returnMessage: string | null = null
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      // Validate inputs
      if (!sharedDocumentId) {
        throw new Error('Invalid shared document ID');
      }
      
      if (!book || !book.id) {
        throw new Error('Invalid book data');
      }
      
      // Check if document exists
      const docRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        throw new Error('Shared document not found');
      }
      
      // Update the shared document status
      await updateDoc(docRef, {
        status: 'completed',
        updatedAt: Timestamp.now(),
        returnMessage: returnMessage
      });
      
      // Update the book data
      const bookRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, 'bookData', 'book');
      await updateDoc(bookRef, {
        ...book,
        updatedAt: Timestamp.now()
      });
      
      return { success: true };
    } catch (err: any) {
      console.error('Error completing shared document:', err);
      const errorMessage = err.message || 'Failed to complete document.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  // Export shared document to PDF
  const exportSharedDocumentToPDF = useCallback((book: Book) => {
    try {
      exportBookToPDF(book);
      return { success: true };
    } catch (err) {
      console.error('Error exporting to PDF:', err);
      setError('Failed to export document to PDF.');
      return { success: false, error: err };
    }
  }, []);

  // Check for returned documents
  const checkForReturnedDocuments = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const q = query(
        collection(db, COLLECTIONS.SHARED_DOCUMENTS),
        where('senderId', '==', userId),
        where('status', '==', 'completed')
      );
      
      const querySnapshot = await getDocs(q);
      const returnedDocs = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      
      return returnedDocs;
    } catch (err) {
      console.error('Error checking for returned documents:', err);
      setError('Failed to check for returned documents.');
      return [];
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // Accept edits from a returned document
  const acceptEdits = useCallback(async (sharedDocumentId: string, originalBookId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Get the shared document data
      const sharedDoc = await getSharedDocument(sharedDocumentId);
      
      if (!sharedDoc || !sharedDoc.book) {
        throw new Error('Shared document not found');
      }
      
      // Get the original book
      const originalBookRef = doc(db, COLLECTIONS.BOOKS, originalBookId);
      const originalBookSnap = await getDoc(originalBookRef);
      
      if (!originalBookSnap.exists()) {
        throw new Error('Original book not found');
      }
      
      // Update the original book with the edited content
      await updateDoc(originalBookRef, {
        chapters: sharedDoc.book.chapters,
        updatedAt: Timestamp.now()
      });
      
      // Get comments from the shared document
      const commentsRef = collection(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId, COLLECTIONS.COMMENTS);
      const commentsSnap = await getDocs(commentsRef);
      const comments = commentsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      
      // Create a subcollection for comments in the original book
      const bookCommentsRef = collection(db, COLLECTIONS.BOOKS, originalBookId, 'editorComments');
      
      // Add all comments to the original book
      for (const comment of comments) {
        await addDoc(bookCommentsRef, {
          ...comment,
          sharedDocumentId,
          importedAt: Timestamp.now()
        });
      }
      
      // Mark the shared document as synced
      const sharedDocRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      await updateDoc(sharedDocRef, {
        syncedWithOriginal: true,
        syncedAt: Timestamp.now(),
        status: 'accepted'
      });
      
      return { success: true };
    } catch (err: any) {
      console.error('Error accepting edits:', err);
      const errorMessage = err.message || 'Failed to accept edits.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, [getSharedDocument]);
  
  // Decline edits from a returned document
  const declineEdits = useCallback(async (sharedDocumentId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // Mark the shared document as declined
      const sharedDocRef = doc(db, COLLECTIONS.SHARED_DOCUMENTS, sharedDocumentId);
      await updateDoc(sharedDocRef, {
        status: 'declined',
        declinedAt: Timestamp.now()
      });
      
      return { success: true };
    } catch (err: any) {
      console.error('Error declining edits:', err);
      const errorMessage = err.message || 'Failed to decline edits.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    shareDocument,
    getSharedWithMe,
    getSharedByMe,
    acceptSharedDocument,
    getSharedDocument,
    addComment,
    getComments,
    updateSharedDocument,
    completeSharedDocument,
    exportSharedDocumentToPDF,
    checkForReturnedDocuments,
    acceptEdits,
    declineEdits
  };
};