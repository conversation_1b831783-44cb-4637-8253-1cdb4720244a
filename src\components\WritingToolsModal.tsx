import React, { useState } from 'react';
import { X, User, <PERSON>bulb, Loader2 } from 'lucide-react';
import { generateCharacterName, generateWritingPrompt } from '../utils/aiUtils';

interface WritingToolsModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentContent: string;
  onSaveCharacter: (character: { name: string; description: string }) => void;
}

export const WritingToolsModal: React.FC<WritingToolsModalProps> = ({
  isOpen,
  onClose,
  currentContent,
  onSaveCharacter
}) => {
  const [showCharacterModal, setShowCharacterModal] = useState(false);
  const [showPromptModal, setShowPromptModal] = useState(false);
  const [characterDescription, setCharacterDescription] = useState('');
  const [generatedCharacterName, setGeneratedCharacterName] = useState('');
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [isGeneratingCharacter, setIsGeneratingCharacter] = useState(false);
  const [isGeneratingPrompt, setIsGeneratingPrompt] = useState(false);

  if (!isOpen) return null;

  const handleGenerateCharacter = async () => {
    if (!characterDescription.trim()) return;
    
    setIsGeneratingCharacter(true);
    try {
      const name = await generateCharacterName(characterDescription);
      setGeneratedCharacterName(name);
    } catch (error) {
      console.error('Error generating character name:', error);
      alert('Failed to generate character name. Please try again.');
    } finally {
      setIsGeneratingCharacter(false);
    }
  };

  const handleGeneratePrompt = async () => {
    setIsGeneratingPrompt(true);
    try {
      const prompt = await generateWritingPrompt(currentContent);
      setGeneratedPrompt(prompt);
    } catch (error) {
      console.error('Error generating writing prompt:', error);
      alert('Failed to generate writing prompt. Please try again.');
    } finally {
      setIsGeneratingPrompt(false);
    }
  };

  const handleSaveCharacter = () => {
    if (generatedCharacterName && characterDescription) {
      onSaveCharacter({
        name: generatedCharacterName,
        description: characterDescription
      });
      setShowCharacterModal(false);
      setCharacterDescription('');
      setGeneratedCharacterName('');
      onClose();
    }
  };

  const resetModals = () => {
    setShowCharacterModal(false);
    setShowPromptModal(false);
    setCharacterDescription('');
    setGeneratedCharacterName('');
    setGeneratedPrompt('');
  };

  const handleClose = () => {
    resetModals();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {!showCharacterModal && !showPromptModal && (
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-serif font-semibold text-gray-900">Writing Tools</h2>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <button
                onClick={() => setShowCharacterModal(true)}
                className="w-full flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors text-left"
              >
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4">
                  <User className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-sans font-semibold text-gray-900">Generate Character Name</h3>
                  <p className="font-sans text-sm text-gray-600">AI-powered character name generation</p>
                </div>
              </button>

              <button
                onClick={() => setShowPromptModal(true)}
                className="w-full flex items-center p-4 bg-green-50 hover:bg-green-100 rounded-xl transition-colors text-left"
              >
                <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mr-4">
                  <Lightbulb className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-sans font-semibold text-gray-900">Get Writing Prompt</h3>
                  <p className="font-sans text-sm text-gray-600">AI suggestions based on your story</p>
                </div>
              </button>
            </div>
          </div>
        )}

        {showCharacterModal && (
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-serif font-semibold text-gray-900">Generate Character</h2>
              <button
                onClick={() => setShowCharacterModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block font-sans font-medium text-gray-700 mb-2">
                  Character Description
                </label>
                <textarea
                  value={characterDescription}
                  onChange={(e) => setCharacterDescription(e.target.value)}
                  placeholder="Describe your character (e.g., a brave knight, mysterious wizard, etc.)"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl font-sans text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={3}
                />
              </div>

              <button
                onClick={handleGenerateCharacter}
                disabled={!characterDescription.trim() || isGeneratingCharacter}
                className="w-full flex items-center justify-center px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-sans font-medium rounded-xl transition-colors"
              >
                {isGeneratingCharacter ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  'Generate Character Name'
                )}
              </button>

              {generatedCharacterName && (
                <div className="p-4 bg-blue-50 rounded-xl">
                  <h3 className="font-sans font-semibold text-gray-900 mb-2">Generated Name:</h3>
                  <p className="font-serif text-lg text-gray-800 mb-4">{generatedCharacterName}</p>
                  <div className="flex space-x-3">
                    <button
                      onClick={handleSaveCharacter}
                      className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-sans font-medium rounded-lg transition-colors"
                    >
                      Save to Project
                    </button>
                    <button
                      onClick={handleGenerateCharacter}
                      disabled={isGeneratingCharacter}
                      className="flex-1 px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-sans font-medium rounded-lg transition-colors"
                    >
                      Generate New
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {showPromptModal && (
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-serif font-semibold text-gray-900">Writing Prompt</h2>
              <button
                onClick={() => setShowPromptModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <p className="font-sans text-gray-600">
                Get AI-powered suggestions based on your current writing to help continue your story.
              </p>

              <button
                onClick={handleGeneratePrompt}
                disabled={isGeneratingPrompt}
                className="w-full flex items-center justify-center px-4 py-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-sans font-medium rounded-xl transition-colors"
              >
                {isGeneratingPrompt ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Analyzing your story...
                  </>
                ) : (
                  'Generate Writing Prompt'
                )}
              </button>

              {generatedPrompt && (
                <div className="p-4 bg-green-50 rounded-xl">
                  <h3 className="font-sans font-semibold text-gray-900 mb-2">Writing Prompt:</h3>
                  <p className="font-serif text-gray-800 leading-relaxed">{generatedPrompt}</p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};