import React, { useState } from 'react';
import { X, Send, Eye, Edit3 } from 'lucide-react';
import { Book } from '../types';

interface ShareDocumentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onShare: (recipientEmail: string, permission: 'view' | 'edit', message: string) => Promise<{ success: boolean }>;
  book: Book;
  backgroundImage: string;
}

export const ShareDocumentModal: React.FC<ShareDocumentModalProps> = ({
  isOpen,
  onClose,
  onShare,
  book,
  backgroundImage
}) => {
  const [recipientEmail, setRecipientEmail] = useState('');
  const [permission, setPermission] = useState<'view' | 'edit'>('view');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    if (!recipientEmail.trim()) {
      setError('Please enter a recipient email');
      setIsLoading(false);
      return;
    }

    try {
      const result = await onShare(recipientEmail.trim(), permission, message);
      if (result.success) {
        setSuccess(true);
        // Reset form after 2 seconds
        setTimeout(() => {
          setRecipientEmail('');
          setPermission('view');
          setMessage('');
          setSuccess(false);
          onClose();
        }, 2000);
      } else {
        setError('Failed to share document. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      <div className="relative z-10 w-full max-w-md bg-white rounded-2xl p-6 shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-serif font-semibold text-gray-800">Share Document</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {success ? (
          <div className="p-4 bg-green-100 border border-green-300 rounded-lg mb-6">
            <p className="text-green-700 font-sans">Document shared successfully!</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-gray-700 font-sans font-medium mb-2">
                Document
              </label>
              <div className="p-3 bg-gray-100 border border-gray-200 rounded-xl">
                <p className="text-gray-800 font-serif">{book.title}</p>
                <p className="text-gray-600 text-sm font-sans mt-1">
                  {book.projectType.charAt(0).toUpperCase() + book.projectType.slice(1).replace('-', ' ')} by {book.author}
                </p>
              </div>
            </div>

            <div>
              <label className="block text-gray-700 font-sans font-medium mb-2">
                Recipient Email
              </label>
              <input
                type="email"
                value={recipientEmail}
                onChange={(e) => setRecipientEmail(e.target.value)}
                className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl text-gray-800 placeholder-gray-500 font-sans focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter recipient's email"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 font-sans font-medium mb-2">
                Permission
              </label>
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => setPermission('view')}
                  className={`flex-1 flex items-center justify-center px-4 py-3 rounded-xl border transition-colors ${
                    permission === 'view'
                      ? 'bg-blue-100 border-blue-300 text-blue-700'
                      : 'bg-gray-100 border-gray-200 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  <span className="font-sans">View Only</span>
                </button>
                <button
                  type="button"
                  onClick={() => setPermission('edit')}
                  className={`flex-1 flex items-center justify-center px-4 py-3 rounded-xl border transition-colors ${
                    permission === 'edit'
                      ? 'bg-blue-100 border-blue-300 text-blue-700'
                      : 'bg-gray-100 border-gray-200 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <Edit3 className="w-4 h-4 mr-2" />
                  <span className="font-sans">Can Edit</span>
                </button>
              </div>
            </div>

            <div>
              <label className="block text-gray-700 font-sans font-medium mb-2">
                Message (Optional)
              </label>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="w-full px-4 py-3 bg-white border border-gray-300 rounded-xl text-gray-800 placeholder-gray-500 font-sans focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Add a message for the recipient..."
                rows={3}
              />
            </div>
            
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
              <p className="text-yellow-800 font-sans text-sm">
                <strong>Note:</strong> The recipient must have a WriterOne account to access this document.
              </p>
            </div>

            {error && (
              <div className="p-3 bg-red-100 border border-red-300 rounded-lg">
                <p className="text-red-700 font-sans text-sm">{error}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex items-center justify-center py-3 bg-blue-600 hover:bg-blue-700 rounded-xl text-white font-sans font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span>Sharing...</span>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  <span>Share Document</span>
                </>
              )}
            </button>
          </form>
        )}
      </div>
    </div>
  );
};