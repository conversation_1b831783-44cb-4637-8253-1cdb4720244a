import { 
  collection, 
  addDoc, 
  query, 
  where, 
  getDocs, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';
import type { ShareActivity } from '../types/sharing';

/**
 * Service for logging and retrieving share activity
 */
export class ShareActivityService {
  
  /**
   * Log a share activity
   */
  static async logActivity(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    action: ShareActivity['action'],
    details: string | null = null,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
    }
  ): Promise<{
    success: boolean;
    activityId?: string;
    error?: string;
  }> {
    try {
      const activity: Omit<ShareActivity, 'id'> = {
        sharedDocumentId,
        userId,
        userName,
        action,
        details,
        timestamp: Timestamp.now(),
        ipAddress: metadata?.ipAddress || null,
        userAgent: metadata?.userAgent || null
      };

      const activityRef = await addDoc(
        collection(db, COLLECTIONS.SHARE_ACTIVITY),
        activity
      );

      return {
        success: true,
        activityId: activityRef.id
      };
    } catch (error) {
      console.error('Error logging share activity:', error);
      return {
        success: false,
        error: 'Failed to log activity'
      };
    }
  }

  /**
   * Get activity log for a shared document
   */
  static async getActivityLog(
    sharedDocumentId: string,
    limitCount: number = 50
  ): Promise<ShareActivity[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.SHARE_ACTIVITY),
        where('sharedDocumentId', '==', sharedDocumentId),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as ShareActivity));
    } catch (error) {
      console.error('Error getting activity log:', error);
      return [];
    }
  }

  /**
   * Get activity log for a user
   */
  static async getUserActivityLog(
    userId: string,
    limitCount: number = 50
  ): Promise<ShareActivity[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.SHARE_ACTIVITY),
        where('userId', '==', userId),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as ShareActivity));
    } catch (error) {
      console.error('Error getting user activity log:', error);
      return [];
    }
  }

  /**
   * Get recent activity across all shares (for admin/monitoring)
   */
  static async getRecentActivity(limitCount: number = 100): Promise<ShareActivity[]> {
    try {
      const q = query(
        collection(db, COLLECTIONS.SHARE_ACTIVITY),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as ShareActivity));
    } catch (error) {
      console.error('Error getting recent activity:', error);
      return [];
    }
  }

  /**
   * Get activity statistics for a shared document
   */
  static async getActivityStats(sharedDocumentId: string): Promise<{
    totalActivities: number;
    uniqueUsers: number;
    lastActivity: Timestamp | null;
    actionCounts: Record<ShareActivity['action'], number>;
  }> {
    try {
      const activities = await this.getActivityLog(sharedDocumentId, 1000); // Get more for stats
      
      const uniqueUsers = new Set(activities.map(a => a.userId)).size;
      const lastActivity = activities.length > 0 ? activities[0].timestamp : null;
      
      const actionCounts: Record<ShareActivity['action'], number> = {
        created: 0,
        accepted: 0,
        viewed: 0,
        edited: 0,
        commented: 0,
        completed: 0,
        declined: 0,
        revoked: 0
      };

      activities.forEach(activity => {
        actionCounts[activity.action] = (actionCounts[activity.action] || 0) + 1;
      });

      return {
        totalActivities: activities.length,
        uniqueUsers,
        lastActivity,
        actionCounts
      };
    } catch (error) {
      console.error('Error getting activity stats:', error);
      return {
        totalActivities: 0,
        uniqueUsers: 0,
        lastActivity: null,
        actionCounts: {
          created: 0,
          accepted: 0,
          viewed: 0,
          edited: 0,
          commented: 0,
          completed: 0,
          declined: 0,
          revoked: 0
        }
      };
    }
  }

  /**
   * Helper method to get client metadata for activity logging
   */
  static getClientMetadata(): {
    ipAddress?: string;
    userAgent?: string;
  } {
    return {
      userAgent: navigator.userAgent,
      // Note: Getting real IP address requires server-side implementation
      // For now, we'll leave it as undefined
      ipAddress: undefined
    };
  }

  /**
   * Convenience methods for common activities
   */
  static async logShareCreated(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    recipientEmail: string
  ) {
    return this.logActivity(
      sharedDocumentId,
      userId,
      userName,
      'created',
      `Shared with ${recipientEmail}`,
      this.getClientMetadata()
    );
  }

  static async logShareAccepted(
    sharedDocumentId: string,
    userId: string,
    userName: string
  ) {
    return this.logActivity(
      sharedDocumentId,
      userId,
      userName,
      'accepted',
      'Share accepted',
      this.getClientMetadata()
    );
  }

  static async logShareViewed(
    sharedDocumentId: string,
    userId: string,
    userName: string
  ) {
    return this.logActivity(
      sharedDocumentId,
      userId,
      userName,
      'viewed',
      'Document viewed',
      this.getClientMetadata()
    );
  }

  static async logShareEdited(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    details?: string
  ) {
    return this.logActivity(
      sharedDocumentId,
      userId,
      userName,
      'edited',
      details || 'Document edited',
      this.getClientMetadata()
    );
  }

  static async logShareCommented(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    commentContent: string
  ) {
    return this.logActivity(
      sharedDocumentId,
      userId,
      userName,
      'commented',
      `Comment: ${commentContent.substring(0, 100)}${commentContent.length > 100 ? '...' : ''}`,
      this.getClientMetadata()
    );
  }

  static async logShareCompleted(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    returnMessage?: string
  ) {
    return this.logActivity(
      sharedDocumentId,
      userId,
      userName,
      'completed',
      returnMessage || 'Editing completed',
      this.getClientMetadata()
    );
  }

  static async logShareDeclined(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    reason?: string
  ) {
    return this.logActivity(
      sharedDocumentId,
      userId,
      userName,
      'declined',
      reason || 'Share declined',
      this.getClientMetadata()
    );
  }

  static async logShareRevoked(
    sharedDocumentId: string,
    userId: string,
    userName: string,
    reason?: string
  ) {
    return this.logActivity(
      sharedDocumentId,
      userId,
      userName,
      'revoked',
      reason || 'Share revoked',
      this.getClientMetadata()
    );
  }
}
