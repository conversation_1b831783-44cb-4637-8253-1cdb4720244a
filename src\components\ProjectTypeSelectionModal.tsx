import React, { useState } from 'react';
import { X, BookOpen, FileText, Scroll, Film, PenTool, Notebook, Heart } from 'lucide-react';
import { ProjectType } from '../types';

interface ProjectTypeOption {
  type: ProjectType;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  wordCountRange: string;
}

const projectTypes: ProjectTypeOption[] = [
  {
    type: 'novel',
    title: 'Novel',
    description: 'A full-length work of fiction with complex characters and plot',
    icon: BookOpen,
    wordCountRange: '80,000+ words'
  },
  {
    type: 'novella',
    title: 'Novella',
    description: 'A shorter work of fiction, longer than a short story',
    icon: FileText,
    wordCountRange: '17,500-40,000 words'
  },
  {
    type: 'short-story',
    title: 'Short Story',
    description: 'A brief work of fiction focusing on a single incident',
    icon: Scroll,
    wordCountRange: '1,000-7,500 words'
  },
  {
    type: 'poem',
    title: 'Poetry Collection',
    description: 'A collection of poems exploring themes and emotions',
    icon: Heart,
    wordCountRange: 'Variable length'
  },
  {
    type: 'screenplay',
    title: 'Screenplay',
    description: 'A script for film or television with dialogue and action',
    icon: Film,
    wordCountRange: '90-120 pages'
  },
  {
    type: 'essay',
    title: 'Essay Collection',
    description: 'Non-fiction pieces exploring ideas and experiences',
    icon: PenTool,
    wordCountRange: '1,000-10,000 words each'
  },
  {
    type: 'journal',
    title: 'Journal/Memoir',
    description: 'Personal reflections and life experiences',
    icon: Notebook,
    wordCountRange: 'Variable length'
  }
];

interface ProjectTypeSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectType: (type: ProjectType) => void;
  backgroundImage: string;
}

export const ProjectTypeSelectionModal: React.FC<ProjectTypeSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectType,
  backgroundImage
}) => {
  const [selectedType, setSelectedType] = useState<ProjectType | null>(null);

  if (!isOpen) return null;

  const handleSelectType = (type: ProjectType) => {
    setSelectedType(type);
    onSelectType(type);
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div 
        className="bg-black/70 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/30">
          <div>
            <h2 className="text-2xl font-serif font-bold text-white">Choose Your Project Type</h2>
            <p className="text-white/90 font-sans mt-1">Select the type of writing project you want to create</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-black/40 rounded-lg transition-colors text-white bg-black/20 backdrop-blur-sm"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {projectTypes.map((option) => {
              const IconComponent = option.icon;
              return (
                <button
                  key={option.type}
                  onClick={() => handleSelectType(option.type)}
                  className="group p-6 bg-black/50 backdrop-blur-md hover:bg-black/60 rounded-xl border border-white/30 hover:border-white/50 transition-all duration-200 text-left hover:scale-105 hover:shadow-xl shadow-lg"
                >
                  <div className="flex items-center mb-4">
                    <div className="p-3 bg-black/40 backdrop-blur-sm rounded-lg mr-4 group-hover:bg-black/50 transition-colors border border-white/20">
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-serif font-semibold text-white">{option.title}</h3>
                      <p className="text-sm text-white/80 font-sans">{option.wordCountRange}</p>
                    </div>
                  </div>

                  <p className="text-white/90 font-sans text-sm leading-relaxed">
                    {option.description}
                  </p>
                </button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
