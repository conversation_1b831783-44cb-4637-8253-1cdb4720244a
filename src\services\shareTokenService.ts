import { 
  collection, 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc,
  query,
  where,
  getDocs,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../lib/firebase';
import { SHARING_COLLECTIONS } from '../types/sharing';
import type { ShareInvitation } from '../types/sharing';

/**
 * Service for managing secure share tokens
 */
export class ShareTokenService {
  
  /**
   * Generate a secure random token
   */
  private static generateSecureToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Create a share invitation with a secure token
   */
  static async createShareInvitation(
    sharedDocumentId: string,
    recipientEmail: string,
    expirationHours: number = 24 * 30 // 30 days default
  ): Promise<{
    success: boolean;
    token?: string;
    shareUrl?: string;
    error?: string;
  }> {
    try {
      const token = this.generateSecureToken();
      const expiresAt = Timestamp.fromDate(
        new Date(Date.now() + expirationHours * 60 * 60 * 1000)
      );

      const invitation: Omit<ShareInvitation, 'id'> = {
        sharedDocumentId,
        recipientEmail: recipientEmail.toLowerCase(),
        token,
        expiresAt,
        usedAt: null,
        createdAt: Timestamp.now()
      };

      const invitationRef = doc(collection(db, SHARING_COLLECTIONS.SHARE_INVITATIONS));
      await setDoc(invitationRef, {
        id: invitationRef.id,
        ...invitation
      });

      const shareUrl = `${window.location.origin}/shared/${sharedDocumentId}?token=${token}`;

      return {
        success: true,
        token,
        shareUrl
      };
    } catch (error) {
      console.error('Error creating share invitation:', error);
      return {
        success: false,
        error: 'Failed to create share invitation'
      };
    }
  }

  /**
   * Validate a share token
   */
  static async validateShareToken(
    sharedDocumentId: string,
    token: string
  ): Promise<{
    isValid: boolean;
    invitation?: ShareInvitation;
    error?: string;
  }> {
    try {
      if (!token || token.length !== 64) {
        return {
          isValid: false,
          error: 'Invalid token format'
        };
      }

      const q = query(
        collection(db, SHARING_COLLECTIONS.SHARE_INVITATIONS),
        where('sharedDocumentId', '==', sharedDocumentId),
        where('token', '==', token)
      );

      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return {
          isValid: false,
          error: 'Token not found'
        };
      }

      const invitation = querySnapshot.docs[0].data() as ShareInvitation;

      // Check if token has expired
      if (invitation.expiresAt && invitation.expiresAt.toDate() < new Date()) {
        return {
          isValid: false,
          error: 'Token has expired'
        };
      }

      // Check if token has already been used (optional, depending on your use case)
      // if (invitation.usedAt) {
      //   return {
      //     isValid: false,
      //     error: 'Token has already been used'
      //   };
      // }

      return {
        isValid: true,
        invitation
      };
    } catch (error) {
      console.error('Error validating share token:', error);
      return {
        isValid: false,
        error: 'Failed to validate token'
      };
    }
  }

  /**
   * Mark a token as used
   */
  static async markTokenAsUsed(
    sharedDocumentId: string,
    token: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const q = query(
        collection(db, SHARING_COLLECTIONS.SHARE_INVITATIONS),
        where('sharedDocumentId', '==', sharedDocumentId),
        where('token', '==', token)
      );

      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return {
          success: false,
          error: 'Token not found'
        };
      }

      const invitationRef = querySnapshot.docs[0].ref;
      await updateDoc(invitationRef, {
        usedAt: Timestamp.now()
      });

      return { success: true };
    } catch (error) {
      console.error('Error marking token as used:', error);
      return {
        success: false,
        error: 'Failed to mark token as used'
      };
    }
  }

  /**
   * Revoke a share token
   */
  static async revokeShareToken(
    sharedDocumentId: string,
    token: string
  ): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const q = query(
        collection(db, SHARING_COLLECTIONS.SHARE_INVITATIONS),
        where('sharedDocumentId', '==', sharedDocumentId),
        where('token', '==', token)
      );

      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return {
          success: false,
          error: 'Token not found'
        };
      }

      const invitationRef = querySnapshot.docs[0].ref;
      await updateDoc(invitationRef, {
        expiresAt: Timestamp.now() // Set expiration to now to effectively revoke
      });

      return { success: true };
    } catch (error) {
      console.error('Error revoking share token:', error);
      return {
        success: false,
        error: 'Failed to revoke token'
      };
    }
  }

  /**
   * Clean up expired tokens (should be run periodically)
   */
  static async cleanupExpiredTokens(): Promise<{
    success: boolean;
    deletedCount?: number;
    error?: string;
  }> {
    try {
      const q = query(
        collection(db, SHARING_COLLECTIONS.SHARE_INVITATIONS),
        where('expiresAt', '<', Timestamp.now())
      );

      const querySnapshot = await getDocs(q);
      
      const deletePromises = querySnapshot.docs.map(doc => doc.ref.delete());
      await Promise.all(deletePromises);

      return {
        success: true,
        deletedCount: querySnapshot.docs.length
      };
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
      return {
        success: false,
        error: 'Failed to cleanup expired tokens'
      };
    }
  }

  /**
   * Get all active invitations for a shared document
   */
  static async getActiveInvitations(sharedDocumentId: string): Promise<ShareInvitation[]> {
    try {
      const q = query(
        collection(db, SHARING_COLLECTIONS.SHARE_INVITATIONS),
        where('sharedDocumentId', '==', sharedDocumentId),
        where('expiresAt', '>', Timestamp.now())
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => doc.data() as ShareInvitation);
    } catch (error) {
      console.error('Error getting active invitations:', error);
      return [];
    }
  }
}
