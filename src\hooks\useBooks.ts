import { useState, useEffect, useCallback, useRef } from 'react'
import {
  collection,
  doc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { db } from '../lib/firebase'
import { Book as FirebaseBook, Chapter as FirebaseChapter, Scene as FirebaseScene, Character as FirebaseCharacter, PlotPoint as FirebasePlotPoint, Location as FirebaseLocation, Institution as FirebaseInstitution, PoliticalSystem as FirebasePoliticalSystem, Hierarchy as FirebaseHierarchy, Culture as FirebaseCulture, Religion as FirebaseReligion, Language as FirebaseLanguage, Technology as FirebaseTechnology, Economy as FirebaseEconomy, Conflict as FirebaseConflict, COLLECTIONS } from '../types/firebase'
import { Book, Chapter, Scene, Character, PlotPoint, ProjectType, Worldbuilding } from '../types'
import { countWords, createEmptyWorldbuilding } from '../utils/bookUtils'
import { migrateBookToWorldbuilding } from '../utils/migrations'

// Helper functions to convert between Firebase and app types
const convertTimestamp = (timestamp: Timestamp): string => {
  return timestamp.toDate().toISOString()
}

const convertFirebaseBook = (firebaseBook: FirebaseBook & { id: string }): Book => ({
  id: firebaseBook.id,
  title: firebaseBook.title,
  author: firebaseBook.author,
  projectType: (firebaseBook.projectType as ProjectType) || 'novel',
  ownerId: firebaseBook.ownerId || firebaseBook.userId, // Support legacy userId field
  createdAt: convertTimestamp(firebaseBook.createdAt),
  updatedAt: convertTimestamp(firebaseBook.updatedAt),
  wordCount: firebaseBook.wordCount,
  settings: firebaseBook.settings,
  chapters: [],
  characters: [],
  plotPoints: [],
  worldbuilding: createEmptyWorldbuilding()
})

export function useBooks(userId: string | undefined) {
  const [books, setBooks] = useState<Book[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (userId) {
      fetchBooks()
    } else {
      setBooks([])
      setLoading(false)
    }
  }, [userId])

  const fetchBooks = async () => {
    if (!userId) return

    try {
      setLoading(true)

      // Fetch books for the user
      const booksQuery = query(
        collection(db, COLLECTIONS.BOOKS),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      )

      const booksSnapshot = await getDocs(booksQuery)
      const booksData: Book[] = []

      for (const bookDoc of booksSnapshot.docs) {
        const firebaseBook = { id: bookDoc.id, ...bookDoc.data() } as FirebaseBook & { id: string }
        const book = convertFirebaseBook(firebaseBook)

        // Fetch chapters for this book
        const chaptersQuery = query(
          collection(db, COLLECTIONS.BOOKS, book.id, 'chapters'),
          orderBy('orderIndex', 'asc')
        )
        const chaptersSnapshot = await getDocs(chaptersQuery)

        for (const chapterDoc of chaptersSnapshot.docs) {
          const firebaseChapter = { id: chapterDoc.id, ...chapterDoc.data() } as FirebaseChapter & { id: string }
          const chapter: Chapter = {
            id: firebaseChapter.id,
            bookId: firebaseChapter.bookId,
            title: firebaseChapter.title,
            content: firebaseChapter.content,
            wordCount: firebaseChapter.wordCount,
            order: firebaseChapter.orderIndex,
            orderIndex: firebaseChapter.orderIndex,
            createdAt: convertTimestamp(firebaseChapter.createdAt),
            updatedAt: convertTimestamp(firebaseChapter.updatedAt),
            scenes: []
          }

          // Fetch scenes for this chapter
          const scenesQuery = query(
            collection(db, COLLECTIONS.BOOKS, book.id, 'chapters', chapter.id, 'scenes'),
            orderBy('orderIndex', 'asc')
          )
          const scenesSnapshot = await getDocs(scenesQuery)

          chapter.scenes = scenesSnapshot.docs.map(sceneDoc => {
            const firebaseScene = { id: sceneDoc.id, ...sceneDoc.data() } as FirebaseScene & { id: string }
            return {
              id: firebaseScene.id,
              chapterId: firebaseScene.chapterId,
              title: firebaseScene.title,
              content: firebaseScene.content,
              wordCount: firebaseScene.wordCount,
              order: firebaseScene.orderIndex,
              orderIndex: firebaseScene.orderIndex,
              createdAt: convertTimestamp(firebaseScene.createdAt),
              updatedAt: convertTimestamp(firebaseScene.updatedAt)
            }
          })

          book.chapters.push(chapter)
        }

        // Fetch characters for this book
        const charactersQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'characters')
        const charactersSnapshot = await getDocs(charactersQuery)

        book.characters = charactersSnapshot.docs.map(characterDoc => {
          const firebaseCharacter = { id: characterDoc.id, ...characterDoc.data() } as FirebaseCharacter & { id: string }
          return {
            id: firebaseCharacter.id,
            bookId: firebaseCharacter.bookId,
            name: firebaseCharacter.name,
            description: firebaseCharacter.description || '',
            role: firebaseCharacter.role || '',
            appearance: firebaseCharacter.appearance || '',
            personality: firebaseCharacter.personality || '',
            backstory: firebaseCharacter.backstory || '',
            goals: firebaseCharacter.goals || '',
            notes: firebaseCharacter.notes || '',
            createdAt: convertTimestamp(firebaseCharacter.createdAt),
            updatedAt: convertTimestamp(firebaseCharacter.updatedAt)
          }
        })

        // Fetch plot points for this book
        const plotPointsQuery = query(
          collection(db, COLLECTIONS.BOOKS, book.id, 'plotPoints'),
          orderBy('orderIndex', 'asc')
        )
        const plotPointsSnapshot = await getDocs(plotPointsQuery)

        book.plotPoints = plotPointsSnapshot.docs.map(plotPointDoc => {
          const firebasePlotPoint = { id: plotPointDoc.id, ...plotPointDoc.data() } as FirebasePlotPoint & { id: string }
          return {
            id: firebasePlotPoint.id,
            bookId: firebasePlotPoint.bookId,
            title: firebasePlotPoint.title,
            description: firebasePlotPoint.description || '',
            chapterId: firebasePlotPoint.chapterId,
            sceneId: firebasePlotPoint.sceneId,
            completed: firebasePlotPoint.completed,
            order: firebasePlotPoint.orderIndex,
            orderIndex: firebasePlotPoint.orderIndex,
            createdAt: convertTimestamp(firebasePlotPoint.createdAt),
            updatedAt: convertTimestamp(firebasePlotPoint.updatedAt)
          }
        })

        // Fetch worldbuilding data
        const worldbuilding = createEmptyWorldbuilding()
        
        try {
          // Fetch locations
          const locationsQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'locations')
          const locationsSnapshot = await getDocs(locationsQuery)
          worldbuilding.locations = locationsSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebaseLocation & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })

          // Fetch institutions
          const institutionsQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'institutions')
          const institutionsSnapshot = await getDocs(institutionsQuery)
          worldbuilding.institutions = institutionsSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebaseInstitution & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })

          // Fetch politics
          const politicsQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'politics')
          const politicsSnapshot = await getDocs(politicsQuery)
          worldbuilding.politics = politicsSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebasePoliticalSystem & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })

          // Fetch hierarchies
          const hierarchiesQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'hierarchies')
          const hierarchiesSnapshot = await getDocs(hierarchiesQuery)
          worldbuilding.hierarchies = hierarchiesSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebaseHierarchy & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })

          // Fetch cultures
          const culturesQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'cultures')
          const culturesSnapshot = await getDocs(culturesQuery)
          worldbuilding.cultures = culturesSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebaseCulture & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })

          // Fetch religions
          const religionsQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'religions')
          const religionsSnapshot = await getDocs(religionsQuery)
          worldbuilding.religions = religionsSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebaseReligion & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })

          // Fetch languages
          const languagesQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'languages')
          const languagesSnapshot = await getDocs(languagesQuery)
          worldbuilding.languages = languagesSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebaseLanguage & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })

          // Fetch technologies
          const technologiesQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'technologies')
          const technologiesSnapshot = await getDocs(technologiesQuery)
          worldbuilding.technologies = technologiesSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebaseTechnology & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })

          // Fetch economies
          const economiesQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'economies')
          const economiesSnapshot = await getDocs(economiesQuery)
          worldbuilding.economies = economiesSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebaseEconomy & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })

          // Fetch conflicts
          const conflictsQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'conflicts')
          const conflictsSnapshot = await getDocs(conflictsQuery)
          worldbuilding.conflicts = conflictsSnapshot.docs.map(doc => {
            const data = { id: doc.id, ...doc.data() } as FirebaseConflict & { id: string }
            return {
              ...data,
              createdAt: convertTimestamp(data.createdAt),
              updatedAt: convertTimestamp(data.updatedAt)
            }
          })
        } catch (worldbuildingError) {
          console.log('Worldbuilding data not found or error fetching, using empty structure')
        }

        book.worldbuilding = worldbuilding
        // Ensure book has proper worldbuilding structure
        const migratedBook = migrateBookToWorldbuilding(book)
        booksData.push(migratedBook)
      }

      setBooks(booksData)
      setError(null)
    } catch (err) {
      console.error('Error fetching books:', err)
      setError('Failed to fetch books')
      // Set empty books array to allow the app to continue
      setBooks([])
    } finally {
      setLoading(false)
    }
  }

  const createBook = async (title: string, author: string, projectType: ProjectType = 'novel') => {
    if (!userId) return null

    try {
      const bookData: Omit<FirebaseBook, 'id'> = {
        userId, // Legacy field for backward compatibility
        ownerId: userId,
        title,
        author,
        projectType,
        wordCount: 0,
        settings: {
          fontSize: 16,
          fontFamily: 'serif',
          theme: 'light',
          lineHeight: 1.6,
          paragraphSpacing: 1.2,
        },
        createdAt: serverTimestamp() as any,
        updatedAt: serverTimestamp() as any
      }

      const docRef = await addDoc(collection(db, COLLECTIONS.BOOKS), bookData)

      const newBook: Book = {
        id: docRef.id,
        title,
        author,
        projectType,
        ownerId: userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        wordCount: 0,
        settings: bookData.settings,
        chapters: [],
        characters: [],
        plotPoints: [],
        worldbuilding: createEmptyWorldbuilding()
      }

      setBooks(prev => [newBook, ...prev])
      return newBook
    } catch (err) {
      console.error('Error creating book:', err)

      // Fallback: Create a local book to unblock the user
      console.log('🔄 Creating fallback book to unblock user')
      const fallbackBook: Book = {
        id: 'fallback-' + Date.now(),
        title,
        author,
        projectType,
        ownerId: userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        wordCount: 0,
        settings: {
          fontSize: 16,
          fontFamily: 'serif',
          theme: 'light',
          lineHeight: 1.6,
          paragraphSpacing: 1.2,
        },
        chapters: [],
        characters: [],
        plotPoints: [],
        worldbuilding: createEmptyWorldbuilding()
      }

      setBooks(prev => [fallbackBook, ...prev])
      return fallbackBook
    }
  }

  const updateBook = async (book: Book) => {
    try {
      // Update book metadata
      const bookRef = doc(db, COLLECTIONS.BOOKS, book.id)
      await updateDoc(bookRef, {
        title: book.title,
        author: book.author,
        wordCount: book.wordCount,
        settings: book.settings,
        updatedAt: serverTimestamp()
      })

      // Get existing chapters from Firebase to compare
      const existingChaptersQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'chapters')
      const existingChaptersSnapshot = await getDocs(existingChaptersQuery)
      const existingChapterIds = existingChaptersSnapshot.docs.map(doc => doc.id)
      const currentChapterIds = book.chapters.map(chapter => chapter.id)
      
      // Delete chapters that are no longer in the book
      for (const existingId of existingChapterIds) {
        if (!currentChapterIds.includes(existingId)) {
          const chapterRef = doc(db, COLLECTIONS.BOOKS, book.id, 'chapters', existingId)
          await deleteDoc(chapterRef)
        }
      }

      // Update or create chapters and scenes
      for (const chapter of book.chapters) {
        const chapterRef = doc(db, COLLECTIONS.BOOKS, book.id, 'chapters', chapter.id)
        const chapterData = {
          bookId: book.id,
          title: chapter.title,
          content: chapter.content || '',
          wordCount: chapter.wordCount,
          orderIndex: chapter.order,
          updatedAt: serverTimestamp()
        }
        
        // Use setDoc with merge to create or update
        await setDoc(chapterRef, {
          ...chapterData,
          createdAt: serverTimestamp()
        }, { merge: true })

        // Get existing scenes from Firebase to compare
        const existingScenesQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'chapters', chapter.id, 'scenes')
        const existingScenesSnapshot = await getDocs(existingScenesQuery)
        const existingSceneIds = existingScenesSnapshot.docs.map(doc => doc.id)
        const currentSceneIds = chapter.scenes.map(scene => scene.id)
        
        // Delete scenes that are no longer in the chapter
        for (const existingId of existingSceneIds) {
          if (!currentSceneIds.includes(existingId)) {
            const sceneRef = doc(db, COLLECTIONS.BOOKS, book.id, 'chapters', chapter.id, 'scenes', existingId)
            await deleteDoc(sceneRef)
          }
        }

        // Update or create scenes
        for (const scene of chapter.scenes) {
          const sceneRef = doc(db, COLLECTIONS.BOOKS, book.id, 'chapters', chapter.id, 'scenes', scene.id)
          const sceneData = {
            chapterId: chapter.id,
            title: scene.title,
            content: scene.content,
            wordCount: countWords(scene.content),
            orderIndex: scene.order,
            updatedAt: serverTimestamp()
          }
          
          // Use setDoc with merge to create or update
          await setDoc(sceneRef, {
            ...sceneData,
            createdAt: serverTimestamp()
          }, { merge: true })
        }
      }

      // Get existing characters from Firebase to compare
      const existingCharactersQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'characters')
      const existingCharactersSnapshot = await getDocs(existingCharactersQuery)
      const existingCharacterIds = existingCharactersSnapshot.docs.map(doc => doc.id)
      const currentCharacterIds = book.characters.map(char => char.id)
      
      // Delete characters that are no longer in the book
      for (const existingId of existingCharacterIds) {
        if (!currentCharacterIds.includes(existingId)) {
          const characterRef = doc(db, COLLECTIONS.BOOKS, book.id, 'characters', existingId)
          await deleteDoc(characterRef)
        }
      }
      
      // Update or create characters
      for (const character of book.characters) {
        const characterRef = doc(db, COLLECTIONS.BOOKS, book.id, 'characters', character.id)
        const characterData = {
          bookId: book.id,
          name: character.name,
          description: character.description,
          role: character.role,
          appearance: character.appearance,
          personality: character.personality,
          backstory: character.backstory,
          goals: character.goals,
          notes: character.notes,
          updatedAt: serverTimestamp()
        }
        
        // Use setDoc with merge to create or update
        await setDoc(characterRef, {
          ...characterData,
          createdAt: serverTimestamp()
        }, { merge: true })
      }

      // Get existing plot points from Firebase to compare
      const existingPlotPointsQuery = collection(db, COLLECTIONS.BOOKS, book.id, 'plotPoints')
      const existingPlotPointsSnapshot = await getDocs(existingPlotPointsQuery)
      const existingPlotPointIds = existingPlotPointsSnapshot.docs.map(doc => doc.id)
      const currentPlotPointIds = book.plotPoints.map(plot => plot.id)
      
      // Delete plot points that are no longer in the book
      for (const existingId of existingPlotPointIds) {
        if (!currentPlotPointIds.includes(existingId)) {
          const plotPointRef = doc(db, COLLECTIONS.BOOKS, book.id, 'plotPoints', existingId)
          await deleteDoc(plotPointRef)
        }
      }
      
      // Update or create plot points
      for (const plotPoint of book.plotPoints) {
        const plotPointRef = doc(db, COLLECTIONS.BOOKS, book.id, 'plotPoints', plotPoint.id)
        const plotPointData = {
          bookId: book.id,
          title: plotPoint.title,
          description: plotPoint.description,
          chapterId: plotPoint.chapterId || null,
          sceneId: plotPoint.sceneId || null,
          completed: plotPoint.completed,
          orderIndex: plotPoint.order,
          updatedAt: serverTimestamp()
        }
        
        // Use setDoc with merge to create or update
        await setDoc(plotPointRef, {
          ...plotPointData,
          createdAt: serverTimestamp()
        }, { merge: true })
      }

      // Update worldbuilding data
      const worldbuildingCategories = [
        { key: 'locations', collection: 'locations' },
        { key: 'institutions', collection: 'institutions' },
        { key: 'politics', collection: 'politics' },
        { key: 'hierarchies', collection: 'hierarchies' },
        { key: 'cultures', collection: 'cultures' },
        { key: 'religions', collection: 'religions' },
        { key: 'languages', collection: 'languages' },
        { key: 'technologies', collection: 'technologies' },
        { key: 'economies', collection: 'economies' },
        { key: 'conflicts', collection: 'conflicts' }
      ]

      for (const category of worldbuildingCategories) {
        const items = book.worldbuilding[category.key as keyof Worldbuilding] as any[]
        
        // Get existing items from Firebase
        const existingQuery = collection(db, COLLECTIONS.BOOKS, book.id, category.collection)
        const existingSnapshot = await getDocs(existingQuery)
        const existingIds = existingSnapshot.docs.map(doc => doc.id)
        const currentIds = items.map(item => item.id)
        
        // Delete items that are no longer in the book
        for (const existingId of existingIds) {
          if (!currentIds.includes(existingId)) {
            const itemRef = doc(db, COLLECTIONS.BOOKS, book.id, category.collection, existingId)
            await deleteDoc(itemRef)
          }
        }
        
        // Update or create items
        for (const item of items) {
          const itemRef = doc(db, COLLECTIONS.BOOKS, book.id, category.collection, item.id)
          const { id, createdAt, updatedAt, ...itemData } = item
          
          await setDoc(itemRef, {
            ...itemData,
            updatedAt: serverTimestamp()
          }, { merge: true })
        }
      }

      // Update local state
      setBooks(prev => prev.map(b => b.id === book.id ? book : b))
    } catch (err) {
      console.error('Error updating book:', err)
      throw err
    }
  }

  const deleteBook = async (bookId: string) => {
    try {
      // Note: In Firestore, deleting a document doesn't automatically delete subcollections
      // For a complete implementation, you'd need to delete all subcollections first
      // For now, we'll just delete the main book document
      const bookRef = doc(db, COLLECTIONS.BOOKS, bookId)
      await deleteDoc(bookRef)

      setBooks(prev => prev.filter(b => b.id !== bookId))
    } catch (err) {
      console.error('Error deleting book:', err)
      throw err
    }
  }

  return {
    books,
    loading,
    error,
    createBook,
    updateBook,
    deleteBook,
    refetch: fetchBooks
  }
}