import { ShareNotification } from '../types/sharing';

/**
 * Email notification service for document sharing
 * This is a placeholder implementation that logs to console.
 * In production, you would integrate with an email service like:
 * - SendGrid
 * - AWS SES
 * - Mailgun
 * - Firebase Functions with Nodemailer
 */
export class EmailNotificationService {
  
  /**
   * Send a share notification email
   */
  static async sendShareNotification(notification: ShareNotification): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // For now, we'll log the notification and return success
      // In production, replace this with actual email sending logic
      console.log('📧 Email Notification:', {
        type: notification.type,
        to: notification.recipientEmail,
        from: notification.senderName,
        subject: this.getEmailSubject(notification),
        body: this.getEmailBody(notification)
      });

      // Simulate email sending delay
      await new Promise(resolve => setTimeout(resolve, 100));

      return { success: true };
    } catch (error) {
      console.error('Error sending email notification:', error);
      return { 
        success: false, 
        error: 'Failed to send email notification' 
      };
    }
  }

  /**
   * Generate email subject based on notification type
   */
  private static getEmailSubject(notification: ShareNotification): string {
    switch (notification.type) {
      case 'share_created':
        return `${notification.senderName} shared "${notification.bookTitle}" with you`;
      case 'share_accepted':
        return `Your shared document "${notification.bookTitle}" was accepted`;
      case 'share_completed':
        return `Edits completed for "${notification.bookTitle}"`;
      case 'share_commented':
        return `New comment on "${notification.bookTitle}"`;
      case 'share_reminder':
        return `Reminder: "${notification.bookTitle}" shared with you`;
      default:
        return `Document sharing notification`;
    }
  }

  /**
   * Generate email body based on notification type
   */
  private static getEmailBody(notification: ShareNotification): string {
    const baseUrl = window.location.origin;
    
    switch (notification.type) {
      case 'share_created':
        return `
Hello!

${notification.senderName} has shared the document "${notification.bookTitle}" with you on WriterOne.

${notification.message ? `Message: "${notification.message}"` : ''}

Click the link below to view the document:
${notification.shareUrl}

${notification.expiresAt ? `This share will expire on ${notification.expiresAt.toDate().toLocaleDateString()}.` : ''}

Best regards,
The WriterOne Team
        `.trim();

      case 'share_accepted':
        return `
Hello ${notification.senderName}!

Great news! Your shared document "${notification.bookTitle}" has been accepted and is now being reviewed.

You'll receive another notification when the review is complete.

Best regards,
The WriterOne Team
        `.trim();

      case 'share_completed':
        return `
Hello ${notification.senderName}!

The edits for your shared document "${notification.bookTitle}" have been completed.

Click the link below to review the changes:
${notification.shareUrl}

Best regards,
The WriterOne Team
        `.trim();

      case 'share_commented':
        return `
Hello!

A new comment has been added to the shared document "${notification.bookTitle}".

Click the link below to view the comment:
${notification.shareUrl}

Best regards,
The WriterOne Team
        `.trim();

      case 'share_reminder':
        return `
Hello!

This is a reminder that ${notification.senderName} shared the document "${notification.bookTitle}" with you.

${notification.expiresAt ? `This share will expire on ${notification.expiresAt.toDate().toLocaleDateString()}.` : ''}

Click the link below to view the document:
${notification.shareUrl}

Best regards,
The WriterOne Team
        `.trim();

      default:
        return `
Hello!

You have a new document sharing notification for "${notification.bookTitle}".

Click the link below to view:
${notification.shareUrl}

Best regards,
The WriterOne Team
        `.trim();
    }
  }

  /**
   * Send multiple notifications (batch)
   */
  static async sendBatchNotifications(notifications: ShareNotification[]): Promise<{
    success: boolean;
    results: Array<{ success: boolean; error?: string }>;
  }> {
    const results = await Promise.all(
      notifications.map(notification => this.sendShareNotification(notification))
    );

    const allSuccessful = results.every(result => result.success);

    return {
      success: allSuccessful,
      results
    };
  }

  /**
   * Validate email configuration (for production setup)
   */
  static validateEmailConfig(): {
    isConfigured: boolean;
    missingConfig: string[];
  } {
    const missingConfig: string[] = [];

    // Add checks for your email service configuration
    // Example for SendGrid:
    // if (!process.env.SENDGRID_API_KEY) missingConfig.push('SENDGRID_API_KEY');
    // if (!process.env.FROM_EMAIL) missingConfig.push('FROM_EMAIL');

    return {
      isConfigured: missingConfig.length === 0,
      missingConfig
    };
  }
}

/**
 * Production Email Service Integration Guide:
 * 
 * 1. Choose an email service (SendGrid, AWS SES, etc.)
 * 2. Install the SDK: npm install @sendgrid/mail
 * 3. Replace the console.log in sendShareNotification with actual email sending
 * 4. Add environment variables for API keys
 * 5. Implement proper error handling and retry logic
 * 
 * Example SendGrid integration:
 * 
 * import sgMail from '@sendgrid/mail';
 * sgMail.setApiKey(process.env.SENDGRID_API_KEY);
 * 
 * const msg = {
 *   to: notification.recipientEmail,
 *   from: process.env.FROM_EMAIL,
 *   subject: this.getEmailSubject(notification),
 *   text: this.getEmailBody(notification),
 *   html: this.getEmailBodyHtml(notification)
 * };
 * 
 * await sgMail.send(msg);
 */
