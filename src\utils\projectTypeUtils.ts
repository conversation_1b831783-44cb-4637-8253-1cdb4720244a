import { ProjectType } from '../types';

export const formatProjectType = (projectType: ProjectType): string => {
  const typeMap: Record<ProjectType, string> = {
    'novel': 'Novel',
    'novella': 'Novella',
    'short-story': 'Short Story',
    'poem': 'Poetry Collection',
    'screenplay': 'Screenplay',
    'essay': 'Essay Collection',
    'journal': 'Journal/Memoir'
  };
  
  return typeMap[projectType] || projectType;
};

export const getProjectTypeIcon = (projectType: ProjectType): string => {
  const iconMap: Record<ProjectType, string> = {
    'novel': '📚',
    'novella': '📖',
    'short-story': '📄',
    'poem': '💝',
    'screenplay': '🎬',
    'essay': '✍️',
    'journal': '📔'
  };
  
  return iconMap[projectType] || '📝';
};

export const getProjectTypeDescription = (projectType: ProjectType): string => {
  const descriptionMap: Record<ProjectType, string> = {
    'novel': 'A full-length work of fiction with complex characters and plot',
    'novella': 'A shorter work of fiction, longer than a short story',
    'short-story': 'A brief work of fiction focusing on a single incident',
    'poem': 'A collection of poems exploring themes and emotions',
    'screenplay': 'A script for film or television with dialogue and action',
    'essay': 'Non-fiction pieces exploring ideas and experiences',
    'journal': 'Personal reflections and life experiences'
  };
  
  return descriptionMap[projectType] || 'A writing project';
};
