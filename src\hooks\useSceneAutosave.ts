import { useState, useEffect, useCallback, useRef } from 'react';

interface SceneHistory {
  sceneId: string;
  content: string;
  lastSaved: Date | null;
  hasUnsavedChanges: boolean;
}

/**
 * Custom hook for scene-specific autosave functionality
 * Maintains separate autosave history for each scene
 */
export function useSceneAutosave(
  sceneId: string | null,
  initialContent: string,
  saveFunction: (sceneId: string, content: string) => Promise<void> | void,
  delay: number = 2000,
  enabled: boolean = true
) {
  const [isSaving, setIsSaving] = useState(false);
  const [sceneHistories, setSceneHistories] = useState<Map<string, SceneHistory>>(new Map());
  const saveRef = useRef(saveFunction);
  const timeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Update save function ref
  useEffect(() => {
    saveRef.current = saveFunction;
  }, [saveFunction]);

  // Get current scene history
  const getCurrentSceneHistory = useCallback((): SceneHistory | null => {
    if (!sceneId) return null;
    return sceneHistories.get(sceneId) || null;
  }, [sceneId, sceneHistories]);

  // Initialize scene history when scene changes
  useEffect(() => {
    if (!sceneId) return;

    setSceneHistories(prev => {
      const newHistories = new Map(prev);
      if (!newHistories.has(sceneId)) {
        newHistories.set(sceneId, {
          sceneId,
          content: initialContent,
          lastSaved: null,
          hasUnsavedChanges: false
        });
      }
      return newHistories;
    });
  }, [sceneId, initialContent]);

  // Update content for current scene
  const updateContent = useCallback((content: string) => {
    if (!sceneId) return;

    setSceneHistories(prev => {
      const newHistories = new Map(prev);
      const currentHistory = newHistories.get(sceneId);
      
      if (currentHistory) {
        newHistories.set(sceneId, {
          ...currentHistory,
          content,
          hasUnsavedChanges: content !== currentHistory.content
        });
      }
      
      return newHistories;
    });

    // Clear existing timeout for this scene
    const existingTimeout = timeoutRefs.current.get(sceneId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout for this scene
    if (enabled) {
      const timeout = setTimeout(async () => {
        const currentHistory = sceneHistories.get(sceneId);
        if (currentHistory && currentHistory.hasUnsavedChanges) {
          setIsSaving(true);
          try {
            await saveRef.current(sceneId, content);
            
            setSceneHistories(prev => {
              const newHistories = new Map(prev);
              const history = newHistories.get(sceneId);
              if (history) {
                newHistories.set(sceneId, {
                  ...history,
                  lastSaved: new Date(),
                  hasUnsavedChanges: false
                });
              }
              return newHistories;
            });
          } catch (error) {
            console.error('Scene autosave failed:', error);
          } finally {
            setIsSaving(false);
          }
        }
      }, delay);

      timeoutRefs.current.set(sceneId, timeout);
    }
  }, [sceneId, delay, enabled, sceneHistories]);

  // Get current content for active scene
  const getCurrentContent = useCallback((): string => {
    const currentHistory = getCurrentSceneHistory();
    return currentHistory?.content || initialContent;
  }, [getCurrentSceneHistory, initialContent]);

  // Get unsaved changes status for current scene
  const hasUnsavedChanges = useCallback((): boolean => {
    const currentHistory = getCurrentSceneHistory();
    return currentHistory?.hasUnsavedChanges || false;
  }, [getCurrentSceneHistory]);

  // Manual save function for current scene
  const saveNow = useCallback(async () => {
    if (!sceneId) return;

    const currentHistory = getCurrentSceneHistory();
    if (!currentHistory || !currentHistory.hasUnsavedChanges) return;

    // Clear timeout for this scene
    const existingTimeout = timeoutRefs.current.get(sceneId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
      timeoutRefs.current.delete(sceneId);
    }

    setIsSaving(true);
    try {
      await saveRef.current(sceneId, currentHistory.content);
      
      setSceneHistories(prev => {
        const newHistories = new Map(prev);
        const history = newHistories.get(sceneId);
        if (history) {
          newHistories.set(sceneId, {
            ...history,
            lastSaved: new Date(),
            hasUnsavedChanges: false
          });
        }
        return newHistories;
      });
    } catch (error) {
      console.error('Manual scene save failed:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [sceneId, getCurrentSceneHistory]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      timeoutRefs.current.forEach(timeout => clearTimeout(timeout));
      timeoutRefs.current.clear();
    };
  }, []);

  // Cleanup timeout when scene changes
  useEffect(() => {
    return () => {
      if (sceneId) {
        const timeout = timeoutRefs.current.get(sceneId);
        if (timeout) {
          clearTimeout(timeout);
          timeoutRefs.current.delete(sceneId);
        }
      }
    };
  }, [sceneId]);

  return {
    isSaving,
    hasUnsavedChanges: hasUnsavedChanges(),
    getCurrentContent,
    updateContent,
    saveNow,
    lastSaved: getCurrentSceneHistory()?.lastSaved || null
  };
}