import React, { useState } from 'react';
import { X, Users, MapPin, Target, Plus, Edit, Trash2 } from 'lucide-react';
import { Book, Character, PlotPoint } from '../types';
import { createNewCharacter, createNewPlotPoint } from '../utils/bookUtils';

interface ProjectElementsModalProps {
  isOpen: boolean;
  onClose: () => void;
  book: Book;
  onUpdateBook: (book: Book) => void;
}

export const ProjectElementsModal: React.FC<ProjectElementsModalProps> = ({
  isOpen,
  onClose,
  book,
  onUpdateBook
}) => {
  const [activeTab, setActiveTab] = useState<'characters' | 'plot' | 'settings'>('characters');
  const [showNewCharacterForm, setShowNewCharacterForm] = useState(false);
  const [showNewPlotForm, setShowNewPlotForm] = useState(false);
  const [editingCharacter, setEditingCharacter] = useState<string | null>(null);
  const [editingPlot, setEditingPlot] = useState<string | null>(null);
  
  // Form states
  const [newCharacterName, setNewCharacterName] = useState('');
  const [newPlotTitle, setNewPlotTitle] = useState('');
  const [characterForm, setCharacterForm] = useState({
    name: '',
    description: '',
    role: '',
    appearance: '',
    personality: '',
    backstory: '',
    goals: '',
    notes: ''
  });
  const [plotForm, setPlotForm] = useState({
    title: '',
    description: ''
  });

  if (!isOpen) return null;

  const handleCreateCharacter = () => {
    if (!newCharacterName.trim()) return;
    
    const newCharacter = createNewCharacter(newCharacterName.trim());
    const updatedBook = {
      ...book,
      characters: [...book.characters, newCharacter],
      updatedAt: new Date().toISOString()
    };
    
    onUpdateBook(updatedBook);
    setNewCharacterName('');
    setShowNewCharacterForm(false);
  };

  const handleCreatePlot = () => {
    if (!newPlotTitle.trim()) return;
    
    const newPlot = createNewPlotPoint(newPlotTitle.trim(), book.plotPoints.length);
    const updatedBook = {
      ...book,
      plotPoints: [...book.plotPoints, newPlot],
      updatedAt: new Date().toISOString()
    };
    
    onUpdateBook(updatedBook);
    setNewPlotTitle('');
    setShowNewPlotForm(false);
  };

  const handleUpdateCharacter = (characterId: string, updates: Partial<Character>) => {
    const updatedBook = {
      ...book,
      characters: book.characters.map(char => 
        char.id === characterId 
          ? { ...char, ...updates, updatedAt: new Date().toISOString() }
          : char
      ),
      updatedAt: new Date().toISOString()
    };
    
    onUpdateBook(updatedBook);
  };

  const handleUpdatePlot = (plotId: string, updates: Partial<PlotPoint>) => {
    const updatedBook = {
      ...book,
      plotPoints: book.plotPoints.map(plot => 
        plot.id === plotId 
          ? { ...plot, ...updates, updatedAt: new Date().toISOString() }
          : plot
      ),
      updatedAt: new Date().toISOString()
    };
    
    onUpdateBook(updatedBook);
  };

  const handleDeleteCharacter = (characterId: string) => {
    if (window.confirm('Are you sure you want to delete this character?')) {
      const updatedBook = {
        ...book,
        characters: book.characters.filter(char => char.id !== characterId),
        updatedAt: new Date().toISOString()
      };
      onUpdateBook(updatedBook);
    }
  };

  const handleDeletePlot = (plotId: string) => {
    if (window.confirm('Are you sure you want to delete this plot point?')) {
      const updatedBook = {
        ...book,
        plotPoints: book.plotPoints.filter(plot => plot.id !== plotId),
        updatedAt: new Date().toISOString()
      };
      onUpdateBook(updatedBook);
    }
  };

  const startEditingCharacter = (character: Character) => {
    setEditingCharacter(character.id);
    setCharacterForm({
      name: character.name,
      description: character.description,
      role: character.role,
      appearance: character.appearance,
      personality: character.personality,
      backstory: character.backstory,
      goals: character.goals,
      notes: character.notes
    });
  };

  const saveCharacterEdit = () => {
    if (!editingCharacter) return;
    handleUpdateCharacter(editingCharacter, characterForm);
    setEditingCharacter(null);
    setCharacterForm({
      name: '',
      description: '',
      role: '',
      appearance: '',
      personality: '',
      backstory: '',
      goals: '',
      notes: ''
    });
  };

  const startEditingPlot = (plot: PlotPoint) => {
    setEditingPlot(plot.id);
    setPlotForm({
      title: plot.title,
      description: plot.description
    });
  };

  const savePlotEdit = () => {
    if (!editingPlot) return;
    handleUpdatePlot(editingPlot, plotForm);
    setEditingPlot(null);
    setPlotForm({ title: '', description: '' });
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-serif font-semibold text-gray-900">Project Elements</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
          
          {/* Tabs */}
          <div className="flex space-x-1 mt-4">
            <button
              onClick={() => setActiveTab('characters')}
              className={`flex items-center px-4 py-2 rounded-lg font-sans font-medium transition-colors ${
                activeTab === 'characters'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <Users className="w-4 h-4 mr-2" />
              Characters
            </button>
            <button
              onClick={() => setActiveTab('plot')}
              className={`flex items-center px-4 py-2 rounded-lg font-sans font-medium transition-colors ${
                activeTab === 'plot'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <Target className="w-4 h-4 mr-2" />
              Plot Points
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`flex items-center px-4 py-2 rounded-lg font-sans font-medium transition-colors ${
                activeTab === 'settings'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <MapPin className="w-4 h-4 mr-2" />
              Settings
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'characters' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-serif font-semibold text-gray-900">Characters</h3>
                <button
                  onClick={() => setShowNewCharacterForm(true)}
                  className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-sans font-medium rounded-lg transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Character
                </button>
              </div>

              {showNewCharacterForm && (
                <div className="p-4 bg-gray-50 rounded-xl">
                  <input
                    type="text"
                    value={newCharacterName}
                    onChange={(e) => setNewCharacterName(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleCreateCharacter()}
                    placeholder="Character name..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans"
                    autoFocus
                  />
                  <div className="flex space-x-2 mt-3">
                    <button
                      onClick={handleCreateCharacter}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-sans font-medium rounded-lg transition-colors"
                    >
                      Add
                    </button>
                    <button
                      onClick={() => setShowNewCharacterForm(false)}
                      className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-sans font-medium rounded-lg transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              <div className="space-y-3">
                {book.characters.map((character) => (
                  <div key={character.id} className="p-4 border border-gray-200 rounded-xl">
                    {editingCharacter === character.id ? (
                      <div className="space-y-3">
                        <input
                          type="text"
                          value={characterForm.name}
                          onChange={(e) => setCharacterForm({ ...characterForm, name: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans font-semibold"
                        />
                        <div className="grid grid-cols-2 gap-3">
                          <input
                            type="text"
                            value={characterForm.role}
                            onChange={(e) => setCharacterForm({ ...characterForm, role: e.target.value })}
                            placeholder="Role (e.g., protagonist, villain)"
                            className="px-3 py-2 border border-gray-300 rounded-lg font-sans text-sm"
                          />
                          <input
                            type="text"
                            value={characterForm.appearance}
                            onChange={(e) => setCharacterForm({ ...characterForm, appearance: e.target.value })}
                            placeholder="Appearance"
                            className="px-3 py-2 border border-gray-300 rounded-lg font-sans text-sm"
                          />
                        </div>
                        <textarea
                          value={characterForm.description}
                          onChange={(e) => setCharacterForm({ ...characterForm, description: e.target.value })}
                          placeholder="Description"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans text-sm resize-none"
                          rows={2}
                        />
                        <textarea
                          value={characterForm.personality}
                          onChange={(e) => setCharacterForm({ ...characterForm, personality: e.target.value })}
                          placeholder="Personality"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans text-sm resize-none"
                          rows={2}
                        />
                        <textarea
                          value={characterForm.backstory}
                          onChange={(e) => setCharacterForm({ ...characterForm, backstory: e.target.value })}
                          placeholder="Backstory"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans text-sm resize-none"
                          rows={2}
                        />
                        <textarea
                          value={characterForm.goals}
                          onChange={(e) => setCharacterForm({ ...characterForm, goals: e.target.value })}
                          placeholder="Goals & Motivations"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans text-sm resize-none"
                          rows={2}
                        />
                        <textarea
                          value={characterForm.notes}
                          onChange={(e) => setCharacterForm({ ...characterForm, notes: e.target.value })}
                          placeholder="Additional notes"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans text-sm resize-none"
                          rows={2}
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={saveCharacterEdit}
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-sans font-medium rounded-lg transition-colors"
                          >
                            Save
                          </button>
                          <button
                            onClick={() => setEditingCharacter(null)}
                            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-sans font-medium rounded-lg transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-serif font-semibold text-gray-900">{character.name}</h4>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => startEditingCharacter(character)}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteCharacter(character.id)}
                              className="text-gray-400 hover:text-red-600"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                        {character.role && (
                          <p className="font-sans text-sm text-blue-600 mb-2">{character.role}</p>
                        )}
                        {character.description && (
                          <p className="font-sans text-sm text-gray-600">{character.description}</p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'plot' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-serif font-semibold text-gray-900">Plot Points</h3>
                <button
                  onClick={() => setShowNewPlotForm(true)}
                  className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-sans font-medium rounded-lg transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Plot Point
                </button>
              </div>

              {showNewPlotForm && (
                <div className="p-4 bg-gray-50 rounded-xl">
                  <input
                    type="text"
                    value={newPlotTitle}
                    onChange={(e) => setNewPlotTitle(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleCreatePlot()}
                    placeholder="Plot point title..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans"
                    autoFocus
                  />
                  <div className="flex space-x-2 mt-3">
                    <button
                      onClick={handleCreatePlot}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-sans font-medium rounded-lg transition-colors"
                    >
                      Add
                    </button>
                    <button
                      onClick={() => setShowNewPlotForm(false)}
                      className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-sans font-medium rounded-lg transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              <div className="space-y-3">
                {book.plotPoints.map((plot) => (
                  <div key={plot.id} className="p-4 border border-gray-200 rounded-xl">
                    {editingPlot === plot.id ? (
                      <div className="space-y-3">
                        <input
                          type="text"
                          value={plotForm.title}
                          onChange={(e) => setPlotForm({ ...plotForm, title: e.target.value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans font-semibold"
                        />
                        <textarea
                          value={plotForm.description}
                          onChange={(e) => setPlotForm({ ...plotForm, description: e.target.value })}
                          placeholder="Plot point description..."
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans text-sm resize-none"
                          rows={3}
                        />
                        <div className="flex space-x-2">
                          <button
                            onClick={savePlotEdit}
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-sans font-medium rounded-lg transition-colors"
                          >
                            Save
                          </button>
                          <button
                            onClick={() => setEditingPlot(null)}
                            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 font-sans font-medium rounded-lg transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              checked={plot.completed}
                              onChange={(e) => handleUpdatePlot(plot.id, { completed: e.target.checked })}
                              className="mr-3 w-4 h-4 text-blue-600 rounded focus:ring-blue-500"
                            />
                            <h4 className={`font-serif font-semibold ${plot.completed ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
                              {plot.title}
                            </h4>
                          </div>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => startEditingPlot(plot)}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDeletePlot(plot.id)}
                              className="text-gray-400 hover:text-red-600"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                        {plot.description && (
                          <p className="font-sans text-sm text-gray-600 ml-7">{plot.description}</p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              <h3 className="text-lg font-serif font-semibold text-gray-900">Book Settings</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block font-sans font-medium text-gray-700 mb-2">
                    Font Family
                  </label>
                  <select
                    value={book.settings.fontFamily}
                    onChange={(e) => {
                      const updatedBook = {
                        ...book,
                        settings: {
                          ...book.settings,
                          fontFamily: e.target.value as 'serif' | 'sans'
                        },
                        updatedAt: new Date().toISOString()
                      };
                      onUpdateBook(updatedBook);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg font-sans"
                  >
                    <option value="serif">EB Garamond (Serif)</option>
                    <option value="sans">Be Vietnam Pro (Sans-serif)</option>
                  </select>
                </div>

                <div>
                  <label className="block font-sans font-medium text-gray-700 mb-2">
                    Font Size: {book.settings.fontSize}px
                  </label>
                  <input
                    type="range"
                    min="12"
                    max="24"
                    value={book.settings.fontSize}
                    onChange={(e) => {
                      const updatedBook = {
                        ...book,
                        settings: {
                          ...book.settings,
                          fontSize: parseInt(e.target.value)
                        },
                        updatedAt: new Date().toISOString()
                      };
                      onUpdateBook(updatedBook);
                    }}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block font-sans font-medium text-gray-700 mb-2">
                    Line Height: {book.settings.lineHeight}
                  </label>
                  <input
                    type="range"
                    min="1.2"
                    max="2.0"
                    step="0.1"
                    value={book.settings.lineHeight}
                    onChange={(e) => {
                      const updatedBook = {
                        ...book,
                        settings: {
                          ...book.settings,
                          lineHeight: parseFloat(e.target.value)
                        },
                        updatedAt: new Date().toISOString()
                      };
                      onUpdateBook(updatedBook);
                    }}
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};