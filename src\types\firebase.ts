import { Timestamp } from 'firebase/firestore'

// User Profile
export interface Profile {
  id: string
  email: string
  fullName: string | null
  avatarUrl: string | null
  onboardingCompleted: boolean
  writingGoals: string[]
  experience: string
  genres: string[]
  dailyGoal: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Book Settings
export interface BookSettings {
  fontSize: number
  fontFamily: string
  theme: string
  lineHeight: number
  paragraphSpacing: number
}

// Book
export interface Book {
  id: string
  userId: string // Legacy field, now maps to ownerId
  ownerId: string
  title: string
  author: string
  projectType: string
  wordCount: number
  settings: BookSettings
  createdAt: Timestamp
  updatedAt: Timestamp
}



// Chapter
export interface Chapter {
  id: string
  bookId: string
  title: string
  content: string
  wordCount: number
  orderIndex: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Scene
export interface Scene {
  id: string
  chapterId: string
  bookId: string // Denormalized for easier querying
  title: string
  content: string
  wordCount: number
  orderIndex: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Character
export interface Character {
  id: string
  bookId: string
  name: string
  description: string | null
  role: string | null
  appearance: string | null
  personality: string | null
  backstory: string | null
  goals: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Plot Point
export interface PlotPoint {
  id: string
  bookId: string
  title: string
  description: string | null
  chapterId: string | null
  sceneId: string | null
  completed: boolean
  orderIndex: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Writing Session
export interface WritingSession {
  id: string
  userId: string
  bookId: string
  date: string // YYYY-MM-DD format
  wordsWritten: number
  timeSpent: number // in minutes
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Writing Goals
export interface WritingGoals {
  id: string
  userId: string
  weekly: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Worldbuilding Types
export interface Location {
  id: string
  bookId: string
  name: string
  type: string
  description: string | null
  geography: string | null
  climate: string | null
  population: string | null
  government: string | null
  economy: string | null
  culture: string | null
  history: string | null
  parentLocationId: string | null
  coordinates: { x: number; y: number } | null
  mapImageUrl: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Institution {
  id: string
  bookId: string
  name: string
  type: string
  description: string | null
  purpose: string | null
  structure: string | null
  leadership: string | null
  membership: string | null
  influence: string
  resources: string | null
  history: string | null
  locationId: string | null
  allies: string | null
  enemies: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface PoliticalSystem {
  id: string
  bookId: string
  name: string
  type: string
  description: string | null
  structure: string | null
  leadership: string | null
  laws: string | null
  enforcement: string | null
  territory: string | null
  relations: string | null
  history: string | null
  locationId: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Hierarchy {
  id: string
  bookId: string
  name: string
  type: string
  description: string | null
  levels: any[]
  mobility: string
  basis: string | null
  privileges: string | null
  responsibilities: string | null
  locationId: string | null
  institutionId: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Culture {
  id: string
  bookId: string
  name: string
  description: string | null
  values: string | null
  traditions: string | null
  customs: string | null
  arts: string | null
  cuisine: string | null
  clothing: string | null
  architecture: string | null
  language: string | null
  religion: string | null
  locationId: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Religion {
  id: string
  bookId: string
  name: string
  type: string
  description: string | null
  beliefs: string | null
  practices: string | null
  clergy: string | null
  temples: string | null
  holidays: string | null
  texts: string | null
  symbols: string | null
  followers: string | null
  influence: string
  locationId: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Language {
  id: string
  bookId: string
  name: string
  type: string
  description: string | null
  speakers: string | null
  regions: string | null
  script: string | null
  grammar: string | null
  vocabulary: string | null
  dialects: string | null
  history: string | null
  status: string
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Technology {
  id: string
  bookId: string
  name: string
  type: string
  description: string | null
  function: string | null
  materials: string | null
  availability: string
  cost: string | null
  requirements: string | null
  limitations: string | null
  inventor: string | null
  history: string | null
  locationId: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Economy {
  id: string
  bookId: string
  name: string
  type: string
  description: string | null
  currency: string | null
  majorIndustries: string | null
  tradeRoutes: string | null
  resources: string | null
  laborForce: string | null
  wealth: string
  inequality: string
  locationId: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface Conflict {
  id: string
  bookId: string
  name: string
  type: string
  description: string | null
  parties: string | null
  causes: string | null
  timeline: string | null
  battles: string | null
  consequences: string | null
  resolution: string | null
  status: string
  locationId: string | null
  notes: string | null
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Firestore Collection Names
export const COLLECTIONS = {
  USERS: 'users',
  BOOKS: 'books',
  CHAPTERS: 'chapters',
  SCENES: 'scenes',
  CHARACTERS: 'characters',
  PLOT_POINTS: 'plotPoints',
  WRITING_SESSIONS: 'writingSessions',
  WRITING_GOALS: 'writingGoals',
  USER_SETTINGS: 'userSettings',
  NOTIFICATIONS: 'notifications',
  LOCATIONS: 'locations',
  INSTITUTIONS: 'institutions',
  POLITICS: 'politics',
  HIERARCHIES: 'hierarchies',
  CULTURES: 'cultures',
  RELIGIONS: 'religions',
  LANGUAGES: 'languages',
  TECHNOLOGIES: 'technologies',
  ECONOMIES: 'economies',
  CONFLICTS: 'conflicts'
} as const

/*
Firestore Data Structure:

/users/{userId} - Profile document
/books/{bookId} - Book document
/books/{bookId}/chapters/{chapterId} - Chapter subcollection
/books/{bookId}/chapters/{chapterId}/scenes/{sceneId} - Scene subcollection
/books/{bookId}/characters/{characterId} - Character subcollection
/books/{bookId}/plotPoints/{plotPointId} - Plot point subcollection

This structure allows for:
1. Easy user data isolation
2. Efficient book-specific queries
3. Hierarchical organization of chapters and scenes
4. Good performance for common operations
*/
