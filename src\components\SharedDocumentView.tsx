import React, { useState, useEffect } from 'react';
import { useDocumentSharing } from '../hooks/useDocumentSharing';
import { SharedDocumentEditor } from './SharedDocumentEditor';
import { Book } from '../types';

interface SharedDocumentViewProps {
  sharedDocumentId: string;
  userId: string;
  userName: string;
  onClose: () => void;
  backgroundImage: string;
}

export const SharedDocumentView: React.FC<SharedDocumentViewProps> = ({
  sharedDocumentId,
  userId,
  userName,
  onClose,
  backgroundImage
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sharedDocument, setSharedDocument] = useState<any>(null);
  const [book, setBook] = useState<Book | null>(null);

  const { getSharedDocument, acceptSharedDocument } = useDocumentSharing({ userId, userName });

  useEffect(() => {
    const loadSharedDocument = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        if (!sharedDocumentId) {
          setError('Invalid shared document ID');
          return;
        }
        
        const result = await getSharedDocument(sharedDocumentId);
        
        if (!result) {
          setError('Shared document not found');
          return;
        }
        
        // Check if user has permission to access this document
        const isSender = result.metadata.senderId === userId;
        const isRecipient = result.metadata.recipientEmail === userId;
        
        if (!isSender && !isRecipient) {
          setError('You do not have permission to access this document');
          return;
        }
        
        setSharedDocument(result.metadata);
        setBook(result.book as Book);
        
        // Auto-accept if pending and user is the recipient
        if (result.metadata.status === 'pending' && isRecipient) {
          try {
            await acceptSharedDocument(sharedDocumentId);
            // Refresh the document data
            const updatedResult = await getSharedDocument(sharedDocumentId);
            if (updatedResult) {
              setSharedDocument(updatedResult.metadata);
            }
          } catch (acceptErr) {
            console.error('Error accepting shared document:', acceptErr);
            // Continue even if accepting fails
          }
        }
      } catch (err: any) {
        console.error('Error loading shared document:', err);
        setError(err.message || 'Failed to load shared document');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadSharedDocument();
  }, [sharedDocumentId, userId, getSharedDocument, acceptSharedDocument]);

  if (isLoading) {
    return (
      <div 
        className="min-h-screen flex items-center justify-center relative"
        style={{
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed'
        }}
      >
        <div className="absolute inset-0 bg-black/30" />
        <div className="relative z-10 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 shadow-2xl">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white mx-auto"></div>
          <p className="text-white font-sans mt-4">Loading shared document...</p>
        </div>
      </div>
    );
  }

  if (error || !sharedDocument || !book) {
    return (
      <div 
        className="min-h-screen flex items-center justify-center relative"
        style={{
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed'
        }}
      >
        <div className="absolute inset-0 bg-black/30" />
        <div className="relative z-10 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 shadow-2xl">
          <h2 className="text-2xl font-serif font-semibold text-white mb-4">Error</h2>
          <p className="text-white/80 font-sans">{error || 'Failed to load shared document'}</p>
          <button
            onClick={onClose}
            className="mt-6 px-4 py-2 bg-white/20 hover:bg-white/30 border border-white/30 rounded-xl text-white font-sans transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <SharedDocumentEditor
      sharedDocument={{ id: sharedDocumentId, metadata: sharedDocument }}
      book={book}
      userId={userId}
      userName={userName}
      onClose={onClose}
      onComplete={onClose}
      backgroundImage={backgroundImage}
    />
  );
};