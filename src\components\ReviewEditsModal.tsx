import React, { useState, useEffect } from 'react';
import { X, Check, MessageSquare, Download } from 'lucide-react';
import { Book } from '../types';

interface ReviewEditsModalProps {
  isOpen: boolean;
  onClose: () => void;
  sharedDocument: any;
  originalBook: Book;
  editedBook: Book;
  onAcceptAllEdits: () => Promise<{ success: boolean }>;
  onDeclineEdits: () => Promise<{ success: boolean }>;
  onExportToPDF: () => void;
  getComments: () => Promise<any[]>;
  backgroundImage: string;
}

export const ReviewEditsModal: React.FC<ReviewEditsModalProps> = ({
  isOpen,
  onClose,
  sharedDocument,
  originalBook,
  editedBook,
  onAcceptAllEdits,
  onDeclineEdits,
  onExportToPDF,
  getComments,
  backgroundImage
}) => {
  const [isAccepting, setIsAccepting] = useState(false);
  const [isDeclining, setIsDeclining] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [comments, setComments] = useState<any[]>([]);
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  
  // Load comments when modal opens
  useEffect(() => {
    if (isOpen) {
      loadComments();
    }
  }, [isOpen]);
  
  const loadComments = async () => {
    setIsLoadingComments(true);
    try {
      const loadedComments = await getComments();
      setComments(loadedComments || []);
    } catch (err) {
      console.error('Error loading comments:', err);
    } finally {
      setIsLoadingComments(false);
    }
  };

  if (!isOpen) return null;

  const handleAcceptAllEdits = async () => {
    setIsAccepting(true);
    setError(null);
    
    try {
      const result = await onAcceptAllEdits();
      
      if (result.success) {
        setSuccess(true);
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setError('Failed to accept edits. Please try again.');
      }
    } catch (err) {
      console.error('Error accepting edits:', err);
      setError('An unexpected error occurred.');
    } finally {
      setIsAccepting(false);
    }
  };
  
  const handleDeclineEdits = async () => {
    if (!confirm('Are you sure you want to decline these edits? This action cannot be undone.')) {
      return;
    }
    
    setIsDeclining(true);
    setError(null);
    
    try {
      const result = await onDeclineEdits();
      
      if (result.success) {
        onClose();
      } else {
        setError('Failed to decline edits. Please try again.');
      }
    } catch (err) {
      console.error('Error declining edits:', err);
      setError('An unexpected error occurred.');
    } finally {
      setIsDeclining(false);
    }
  };

  // Calculate changes between original and edited book
  const getChangeSummary = () => {
    const changedChapters = editedBook.chapters.filter(editedChapter => {
      const originalChapter = originalBook.chapters.find(c => c.id === editedChapter.id);
      if (!originalChapter) return true; // New chapter
      
      // Check if any scenes were changed
      return editedChapter.scenes.some(editedScene => {
        const originalScene = originalChapter.scenes.find(s => s.id === editedScene.id);
        if (!originalScene) return true; // New scene
        return editedScene.content !== originalScene.content;
      });
    });
    
    return {
      changedChaptersCount: changedChapters.length,
      totalChaptersCount: editedBook.chapters.length,
      editorName: sharedDocument.metadata?.senderName || 'Editor'
    };
  };

  const summary = getChangeSummary();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      <div className="relative z-10 w-full max-w-2xl bg-white rounded-2xl p-6 shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-serif font-semibold text-gray-800">Review Edits</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="mb-6">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl mb-4">
            <h3 className="text-lg font-medium text-gray-800 mb-2">{editedBook.title}</h3>
            <p className="text-gray-600 mb-2">
              {summary.editorName} has made changes to {summary.changedChaptersCount} out of {summary.totalChaptersCount} chapters.
            </p>
            {sharedDocument.metadata?.returnMessage && (
              <div className="mt-3 p-3 bg-gray-100 border border-gray-200 rounded-lg">
                <p className="text-gray-700 font-sans text-sm italic">"{sharedDocument.metadata.returnMessage}"</p>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-gray-800 font-medium">Accept All Changes</h4>
                <p className="text-gray-600 text-sm">
                  This will update your original document with all the changes made by the editor.
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleDeclineEdits}
                  disabled={isDeclining || success}
                  className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isDeclining ? (
                    <span>Processing...</span>
                  ) : (
                    <span>Decline</span>
                  )}
                </button>
                <button
                  onClick={handleAcceptAllEdits}
                  disabled={isAccepting || success}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isAccepting ? (
                    <span>Processing...</span>
                  ) : success ? (
                    <>
                      <Check className="w-4 h-4 mr-2" />
                      <span>Changes Accepted</span>
                    </>
                  ) : (
                    <>
                      <Check className="w-4 h-4 mr-2" />
                      <span>Accept All Edits</span>
                    </>
                  )}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-gray-800 font-medium">Export to PDF</h4>
                <p className="text-gray-600 text-sm">
                  Download a PDF version of the edited document with all changes.
                </p>
              </div>
              <button
                onClick={onExportToPDF}
                className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg flex items-center"
              >
                <Download className="w-4 h-4 mr-2" />
                <span>Export PDF</span>
              </button>
            </div>

            {/* Comments Section */}
            <div className="mt-6 border-t border-gray-200 pt-6">
              <h4 className="text-gray-800 font-medium mb-4">Editor Comments</h4>
              
              {isLoadingComments ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                </div>
              ) : comments.length > 0 ? (
                <div className="space-y-4 max-h-64 overflow-y-auto">
                  {comments.map((comment) => (
                    <div 
                      key={comment.id} 
                      className="p-3 bg-gray-50 border border-gray-200 rounded-lg"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-800 font-sans font-medium">{comment.userName}</span>
                        <span className="text-gray-500 text-xs">
                          {new Date(comment.createdAt.seconds * 1000).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-gray-700 font-sans">{comment.content}</p>
                      {comment.position && (
                        <div className="mt-2 text-xs text-gray-500 font-sans">
                          {editedBook.chapters.find(c => c.id === comment.position.chapterId)?.title} - 
                          {editedBook.chapters
                            .find(c => c.id === comment.position.chapterId)
                            ?.scenes.find(s => s.id === comment.position.sceneId)?.title}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg text-center">
                  <p className="text-gray-500 font-sans">No comments from the editor</p>
                </div>
              )}
            </div>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded-lg">
              <p className="text-red-700 font-sans text-sm">{error}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};