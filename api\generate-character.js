import { GoogleGenerativeAI } from '@google/generative-ai';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { description } = req.body;

  if (!description) {
    return res.status(400).json({ error: 'Description is required' });
  }

  const apiKey = process.env.GEMINI_API_KEY;
  
  if (!apiKey) {
    return res.status(500).json({ error: 'API key not configured' });
  }

  try {
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash-lite-preview-06-17' });
    
    const prompt = `Generate a single, creative character name for a character described as: "${description}". 
    The name should be fitting for the character's description and suitable for a novel. 
    Return only the character name, nothing else.`;
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const name = response.text().trim();
    
    res.status(200).json({ name });
  } catch (error) {
    console.error('Error generating character name:', error);
    res.status(500).json({ error: 'Failed to generate character name' });
  }
}