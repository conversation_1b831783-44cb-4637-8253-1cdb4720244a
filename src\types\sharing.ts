import { Timestamp } from 'firebase/firestore';

export type SharePermission = 'view' | 'edit';
export type ShareStatus = 'pending' | 'accepted' | 'completed' | 'declined' | 'expired' | 'revoked';

export interface SharedDocument {
  id: string;
  bookId: string;
  bookTitle: string;
  senderId: string; // Firebase UID
  senderName: string;
  senderEmail: string;
  recipientId: string | null; // Firebase UID (null if recipient not found)
  recipientEmail: string;
  recipientName: string | null;
  permission: SharePermission;
  status: ShareStatus;
  message: string | null;
  returnMessage: string | null;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  expiresAt: Timestamp | null;
  acceptedAt: Timestamp | null;
  completedAt: Timestamp | null;
  revokedAt: Timestamp | null;
  lastAccessedAt: Timestamp | null;
  accessCount: number;
}

export interface Comment {
  id: string;
  sharedDocumentId: string;
  userId: string; // Firebase UID
  userName: string;
  userEmail: string;
  content: string;
  position: {
    chapterId: string;
    sceneId: string;
    textPosition: number;
  } | null;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  editedAt: Timestamp | null;
  isResolved: boolean;
  resolvedBy: string | null;
  resolvedAt: Timestamp | null;
}

export interface ShareInvitation {
  id: string;
  sharedDocumentId: string;
  recipientEmail: string;
  token: string; // Secure token for accessing the share
  expiresAt: Timestamp;
  usedAt: Timestamp | null;
  createdAt: Timestamp;
}

export interface ShareActivity {
  id: string;
  sharedDocumentId: string;
  userId: string;
  userName: string;
  action: 'created' | 'accepted' | 'viewed' | 'edited' | 'commented' | 'completed' | 'declined' | 'revoked';
  details: string | null;
  timestamp: Timestamp;
  ipAddress: string | null;
  userAgent: string | null;
}

// Email notification types
export interface ShareNotification {
  type: 'share_created' | 'share_accepted' | 'share_completed' | 'share_commented' | 'share_reminder';
  recipientEmail: string;
  senderName: string;
  bookTitle: string;
  shareUrl: string;
  message?: string;
  expiresAt?: Timestamp;
}

// User lookup for email-to-UID mapping
export interface UserLookup {
  email: string;
  uid: string;
  displayName: string | null;
  lastSeen: Timestamp;
  isActive: boolean;
}

// Update the COLLECTIONS constant in firebase.ts
export const SHARING_COLLECTIONS = {
  SHARED_DOCUMENTS: 'sharedDocuments',
  COMMENTS: 'comments',
  SHARE_INVITATIONS: 'shareInvitations',
  SHARE_ACTIVITY: 'shareActivity',
  USER_LOOKUP: 'userLookup'
} as const;