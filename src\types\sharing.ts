import { Timestamp } from 'firebase/firestore';

export type SharePermission = 'view' | 'edit';
export type ShareStatus = 'pending' | 'accepted' | 'completed';

export interface SharedDocument {
  id: string;
  bookId: string;
  senderId: string;
  senderName: string;
  recipientEmail: string;
  permission: SharePermission;
  status: ShareStatus;
  message: string | null;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  expiresAt: Timestamp | null;
}

export interface Comment {
  id: string;
  sharedDocumentId: string;
  userId: string;
  userName: string;
  content: string;
  position: {
    chapterId: string;
    sceneId: string;
    textPosition: number;
  } | null;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Update the COLLECTIONS constant in firebase.ts
export const SHARING_COLLECTIONS = {
  SHARED_DOCUMENTS: 'sharedDocuments',
  COMMENTS: 'comments'
} as const;