import React, { useState, useEffect } from 'react';
import { X, FileText, Clock, CheckCircle, ArrowRight, Send, Download } from 'lucide-react';
import { useDocumentSharing } from '../hooks/useDocumentSharing';

interface SharedDocumentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string; // Firebase UID
  userName: string;
  userEmail: string;
  onOpenSharedDocument: (sharedDocumentId: string) => void;
  backgroundImage: string;
}

export const SharedDocumentsModal: React.FC<SharedDocumentsModalProps> = ({
  isOpen,
  onClose,
  userId,
  userName,
  userEmail,
  onOpenSharedDocument,
  backgroundImage
}) => {
  const [activeTab, setActiveTab] = useState<'shared-with-me' | 'shared-by-me'>('shared-with-me');
  const [sharedWithMe, setSharedWithMe] = useState<any[]>([]);
  const [sharedByMe, setSharedByMe] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { getSharedWithMe, getSharedByMe } = useDocumentSharing({ userId, userName, userEmail });

  useEffect(() => {
    if (isOpen) {
      loadSharedDocuments();
    }
  }, [isOpen, activeTab]);

  const loadSharedDocuments = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (activeTab === 'shared-with-me') {
        const docs = await getSharedWithMe();
        setSharedWithMe(docs);
      } else {
        const docs = await getSharedByMe();
        setSharedByMe(docs);
      }
    } catch (err) {
      setError('Failed to load shared documents');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <div className="flex items-center px-2 py-1 bg-yellow-500/20 border border-yellow-500/30 rounded text-yellow-200 text-xs">
            <Clock className="w-3 h-3 mr-1" />
            <span>Pending</span>
          </div>
        );
      case 'accepted':
        return (
          <div className="flex items-center px-2 py-1 bg-blue-500/20 border border-blue-500/30 rounded text-blue-200 text-xs">
            <CheckCircle className="w-3 h-3 mr-1" />
            <span>In Progress</span>
          </div>
        );
      case 'completed':
        return (
          <div className="flex items-center px-2 py-1 bg-green-500/20 border border-green-500/30 rounded text-green-200 text-xs">
            <CheckCircle className="w-3 h-3 mr-1" />
            <span>Completed</span>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />
      
      <div className="relative z-10 w-full max-w-2xl bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 shadow-2xl">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-serif font-semibold text-white">Shared Documents</h2>
          <button 
            onClick={onClose}
            className="text-white/60 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex border-b border-white/20 mb-6">
          <button
            onClick={() => setActiveTab('shared-with-me')}
            className={`px-4 py-2 font-sans transition-colors ${
              activeTab === 'shared-with-me'
                ? 'text-white border-b-2 border-white'
                : 'text-white/60 hover:text-white'
            }`}
          >
            Shared with Me
          </button>
          <button
            onClick={() => setActiveTab('shared-by-me')}
            className={`px-4 py-2 font-sans transition-colors ${
              activeTab === 'shared-by-me'
                ? 'text-white border-b-2 border-white'
                : 'text-white/60 hover:text-white'
            }`}
          >
            Shared by Me
          </button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
          </div>
        ) : error ? (
          <div className="p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
            <p className="text-red-200 font-sans">{error}</p>
          </div>
        ) : (
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {activeTab === 'shared-with-me' ? (
              sharedWithMe.length > 0 ? (
                sharedWithMe.map((doc: any) => (
                  <div 
                    key={doc.id} 
                    className="p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center">
                          <FileText className="w-5 h-5 text-white/70 mr-2" />
                          <h3 className="text-lg font-serif text-white">{doc.bookTitle || 'Shared Document'}</h3>
                        </div>
                        <p className="text-white/60 text-sm font-sans mt-1">
                          Shared by {doc.senderName} on {formatDate(doc.createdAt)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(doc.status)}
                        <button
                          onClick={() => onOpenSharedDocument(doc.id)}
                          className="flex items-center px-3 py-2 bg-white/20 hover:bg-white/30 border border-white/30 rounded-lg text-white text-sm font-sans transition-colors"
                        >
                          <ArrowRight className="w-4 h-4 mr-1" />
                          <span>Open</span>
                        </button>
                      </div>
                    </div>
                    {doc.message && (
                      <div className="mt-3 p-3 bg-white/10 rounded-lg">
                        <p className="text-white/80 text-sm font-sans italic">"{doc.message}"</p>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-white/60 font-sans">No documents have been shared with you</p>
                </div>
              )
            ) : (
              sharedByMe.length > 0 ? (
                sharedByMe.map((doc: any) => (
                  <div 
                    key={doc.id} 
                    className="p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center">
                          <FileText className="w-5 h-5 text-white/70 mr-2" />
                          <h3 className="text-lg font-serif text-white">{doc.bookTitle || 'Shared Document'}</h3>
                        </div>
                        <p className="text-white/60 text-sm font-sans mt-1">
                          Shared with {doc.recipientEmail} on {formatDate(doc.createdAt)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(doc.status)}
                        {doc.status === 'completed' ? (
                          <button
                            onClick={() => onOpenSharedDocument(doc.id)}
                            className="flex items-center px-3 py-2 bg-white/20 hover:bg-white/30 border border-white/30 rounded-lg text-white text-sm font-sans transition-colors"
                          >
                            <Download className="w-4 h-4 mr-1" />
                            <span>View Changes</span>
                          </button>
                        ) : (
                          <button
                            onClick={() => onOpenSharedDocument(doc.id)}
                            className="flex items-center px-3 py-2 bg-white/20 hover:bg-white/30 border border-white/30 rounded-lg text-white text-sm font-sans transition-colors"
                          >
                            <ArrowRight className="w-4 h-4 mr-1" />
                            <span>View</span>
                          </button>
                        )}
                      </div>
                    </div>
                    {doc.message && (
                      <div className="mt-3 p-3 bg-white/10 rounded-lg">
                        <p className="text-white/80 text-sm font-sans italic">"{doc.message}"</p>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-white/60 font-sans">You haven't shared any documents yet</p>
                </div>
              )
            )}
          </div>
        )}
      </div>
    </div>
  );
};