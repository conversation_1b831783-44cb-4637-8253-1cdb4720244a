/**
 * Comprehensive test suite for document sharing functionality
 * 
 * This file contains manual tests to verify the document sharing system works correctly.
 * Run these tests in the browser console or create a proper test runner.
 */

import { UserLookupService } from '../services/userLookupService';
import { EmailNotificationService } from '../services/emailNotificationService';
import { ShareTokenService } from '../services/shareTokenService';
import { ShareActivityService } from '../services/shareActivityService';
import { ShareCleanupService } from '../services/shareCleanupService';
import { RealtimeCollaborationService } from '../services/realtimeCollaborationService';

/**
 * Test user lookup functionality
 */
export async function testUserLookup() {
  console.log('🧪 Testing User Lookup Service...');
  
  try {
    // Test user registration
    await UserLookupService.registerUser('test-uid-123', '<EMAIL>', 'Test User');
    console.log('✅ User registration successful');
    
    // Test user lookup by email
    const user = await UserLookupService.findUserByEmail('<EMAIL>');
    console.log('✅ User lookup by email:', user);
    
    // Test email validation
    const validation = await UserLookupService.validateRecipientEmail('<EMAIL>');
    console.log('✅ Email validation:', validation);
    
    // Test invalid email
    const invalidValidation = await UserLookupService.validateRecipientEmail('<EMAIL>');
    console.log('✅ Invalid email validation:', invalidValidation);
    
  } catch (error) {
    console.error('❌ User lookup test failed:', error);
  }
}

/**
 * Test email notification functionality
 */
export async function testEmailNotifications() {
  console.log('🧪 Testing Email Notification Service...');
  
  try {
    // Test share notification
    const result = await EmailNotificationService.sendShareNotification({
      type: 'share_created',
      recipientEmail: '<EMAIL>',
      senderName: 'Test Sender',
      bookTitle: 'Test Book',
      shareUrl: 'https://example.com/shared/123',
      message: 'Please review this document'
    });
    
    console.log('✅ Email notification result:', result);
    
    // Test batch notifications
    const batchResult = await EmailNotificationService.sendBatchNotifications([
      {
        type: 'share_accepted',
        recipientEmail: '<EMAIL>',
        senderName: 'Test Recipient',
        bookTitle: 'Test Book',
        shareUrl: 'https://example.com/shared/123'
      }
    ]);
    
    console.log('✅ Batch email result:', batchResult);
    
  } catch (error) {
    console.error('❌ Email notification test failed:', error);
  }
}

/**
 * Test share token functionality
 */
export async function testShareTokens() {
  console.log('🧪 Testing Share Token Service...');
  
  try {
    // Test token creation
    const tokenResult = await ShareTokenService.createShareInvitation(
      'test-share-123',
      '<EMAIL>',
      24 // 24 hours
    );
    
    console.log('✅ Token creation result:', tokenResult);
    
    if (tokenResult.success && tokenResult.token) {
      // Test token validation
      const validation = await ShareTokenService.validateShareToken(
        'test-share-123',
        tokenResult.token
      );
      
      console.log('✅ Token validation result:', validation);
      
      // Test marking token as used
      const markUsedResult = await ShareTokenService.markTokenAsUsed(
        'test-share-123',
        tokenResult.token
      );
      
      console.log('✅ Mark token as used result:', markUsedResult);
    }
    
  } catch (error) {
    console.error('❌ Share token test failed:', error);
  }
}

/**
 * Test activity logging
 */
export async function testActivityLogging() {
  console.log('🧪 Testing Share Activity Service...');
  
  try {
    // Test activity logging
    const logResult = await ShareActivityService.logShareCreated(
      'test-share-123',
      'test-user-123',
      'Test User',
      '<EMAIL>'
    );
    
    console.log('✅ Activity logging result:', logResult);
    
    // Test getting activity log
    const activities = await ShareActivityService.getActivityLog('test-share-123');
    console.log('✅ Activity log:', activities);
    
    // Test activity stats
    const stats = await ShareActivityService.getActivityStats('test-share-123');
    console.log('✅ Activity stats:', stats);
    
  } catch (error) {
    console.error('❌ Activity logging test failed:', error);
  }
}

/**
 * Test cleanup functionality
 */
export async function testCleanup() {
  console.log('🧪 Testing Share Cleanup Service...');
  
  try {
    // Test cleanup stats
    const stats = await ShareCleanupService.getCleanupStats();
    console.log('✅ Cleanup stats:', stats);
    
    // Test expired shares cleanup
    const expiredResult = await ShareCleanupService.markExpiredShares();
    console.log('✅ Expired shares cleanup:', expiredResult);
    
    // Test token cleanup
    const tokenCleanup = await ShareCleanupService.cleanupExpiredTokens();
    console.log('✅ Token cleanup:', tokenCleanup);
    
    // Test full cleanup
    const fullCleanup = await ShareCleanupService.runFullCleanup();
    console.log('✅ Full cleanup result:', fullCleanup);
    
  } catch (error) {
    console.error('❌ Cleanup test failed:', error);
  }
}

/**
 * Test real-time collaboration
 */
export async function testRealtimeCollaboration() {
  console.log('🧪 Testing Realtime Collaboration Service...');
  
  try {
    // Test adding a comment
    const commentResult = await RealtimeCollaborationService.addRealtimeComment(
      'test-share-123',
      'test-user-123',
      'Test User',
      '<EMAIL>',
      'This is a test comment',
      {
        chapterId: 'chapter-1',
        sceneId: 'scene-1',
        textPosition: 100
      }
    );
    
    console.log('✅ Comment creation result:', commentResult);
    
    // Test presence update
    await RealtimeCollaborationService.updateUserPresence(
      'test-share-123',
      'test-user-123',
      'Test User',
      'editing'
    );
    
    console.log('✅ Presence update successful');
    
    // Test conflict resolution
    const conflictResolution = RealtimeCollaborationService.getConflictResolutionSuggestions(
      { content: 'Local content', lastEditedAt: new Date() },
      { content: 'Remote content', lastEditedAt: new Date(Date.now() + 1000) }
    );
    
    console.log('✅ Conflict resolution suggestions:', conflictResolution);
    
  } catch (error) {
    console.error('❌ Realtime collaboration test failed:', error);
  }
}

/**
 * Run all tests
 */
export async function runAllTests() {
  console.log('🚀 Starting comprehensive document sharing tests...');
  
  await testUserLookup();
  await testEmailNotifications();
  await testShareTokens();
  await testActivityLogging();
  await testCleanup();
  await testRealtimeCollaboration();
  
  console.log('🎉 All tests completed!');
}

/**
 * Test the complete sharing workflow
 */
export async function testCompleteWorkflow() {
  console.log('🧪 Testing Complete Sharing Workflow...');
  
  try {
    // 1. Register users
    await UserLookupService.registerUser('sender-123', '<EMAIL>', 'Sender User');
    await UserLookupService.registerUser('recipient-123', '<EMAIL>', 'Recipient User');
    
    // 2. Validate recipient
    const validation = await UserLookupService.validateRecipientEmail('<EMAIL>');
    console.log('✅ Recipient validation:', validation.isValid);
    
    // 3. Create share token
    const tokenResult = await ShareTokenService.createShareInvitation(
      'workflow-test-share',
      '<EMAIL>',
      24
    );
    console.log('✅ Share token created:', tokenResult.success);
    
    // 4. Send notification
    if (tokenResult.success) {
      const emailResult = await EmailNotificationService.sendShareNotification({
        type: 'share_created',
        recipientEmail: '<EMAIL>',
        senderName: 'Sender User',
        bookTitle: 'Test Workflow Book',
        shareUrl: tokenResult.shareUrl || 'https://example.com/shared/workflow-test-share'
      });
      console.log('✅ Email notification sent:', emailResult.success);
    }
    
    // 5. Log activity
    await ShareActivityService.logShareCreated(
      'workflow-test-share',
      'sender-123',
      'Sender User',
      '<EMAIL>'
    );
    console.log('✅ Activity logged');
    
    // 6. Simulate recipient accepting
    await ShareActivityService.logShareAccepted(
      'workflow-test-share',
      'recipient-123',
      'Recipient User'
    );
    console.log('✅ Share acceptance logged');
    
    // 7. Add a comment
    const commentResult = await RealtimeCollaborationService.addRealtimeComment(
      'workflow-test-share',
      'recipient-123',
      'Recipient User',
      '<EMAIL>',
      'Great document! I have some suggestions.'
    );
    console.log('✅ Comment added:', commentResult.success);
    
    console.log('🎉 Complete workflow test successful!');
    
  } catch (error) {
    console.error('❌ Complete workflow test failed:', error);
  }
}

// Export test functions for manual execution
(window as any).documentSharingTests = {
  runAllTests,
  testCompleteWorkflow,
  testUserLookup,
  testEmailNotifications,
  testShareTokens,
  testActivityLogging,
  testCleanup,
  testRealtimeCollaboration
};

console.log('📋 Document sharing tests loaded. Run tests with:');
console.log('- documentSharingTests.runAllTests()');
console.log('- documentSharingTests.testCompleteWorkflow()');
console.log('- Or individual test functions');
