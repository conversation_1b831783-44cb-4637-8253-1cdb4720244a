import { UserLookupService } from '../services/userLookupService';
import { auth } from '../lib/firebase';

/**
 * Utility functions for syncing users to the lookup table
 */

/**
 * Sync the current logged-in user to the lookup table
 */
export async function syncCurrentUserToLookup(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const currentUser = auth.currentUser;
    
    if (!currentUser) {
      return {
        success: false,
        error: 'No user is currently logged in'
      };
    }

    await UserLookupService.syncCurrentUser(
      currentUser.uid,
      currentUser.email || '',
      currentUser.displayName
    );

    console.log('✅ Current user synced to lookup table');
    return { success: true };
  } catch (error) {
    console.error('❌ Failed to sync current user:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Manual function to add a user to the lookup table by email
 * This is a temporary workaround for existing users
 */
export async function manuallyAddUserToLookup(
  email: string,
  displayName?: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Generate a temporary UID for the user
    // In a real implementation, you'd get this from Firebase Admin SDK
    const tempUid = `manual-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    await UserLookupService.registerUser(
      tempUid,
      email.toLowerCase(),
      displayName || email.split('@')[0]
    );

    console.log(`✅ Manually added user to lookup: ${email}`);
    return { success: true };
  } catch (error) {
    console.error(`❌ Failed to manually add user ${email}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Batch add multiple users to the lookup table
 */
export async function batchAddUsersToLookup(
  users: Array<{ email: string; displayName?: string }>
): Promise<{
  success: number;
  failed: number;
  errors: string[];
}> {
  const results = {
    success: 0,
    failed: 0,
    errors: [] as string[]
  };

  console.log(`🔄 Starting batch add of ${users.length} users...`);

  for (const user of users) {
    const result = await manuallyAddUserToLookup(user.email, user.displayName);
    
    if (result.success) {
      results.success++;
    } else {
      results.failed++;
      results.errors.push(`${user.email}: ${result.error}`);
    }
  }

  console.log(`🎉 Batch add complete: ${results.success} success, ${results.failed} failed`);
  return results;
}

/**
 * Check if a user exists in the lookup table
 */
export async function checkUserInLookup(email: string): Promise<{
  exists: boolean;
  user?: any;
}> {
  try {
    const user = await UserLookupService.findUserByEmail(email);
    return {
      exists: !!user,
      user
    };
  } catch (error) {
    console.error('Error checking user in lookup:', error);
    return { exists: false };
  }
}

/**
 * Get all users in the lookup table (for debugging)
 */
export async function getAllLookupUsers(): Promise<any[]> {
  try {
    return await UserLookupService.getActiveUsers(100);
  } catch (error) {
    console.error('Error getting lookup users:', error);
    return [];
  }
}

// Make these functions available globally for debugging
declare global {
  interface Window {
    userSyncUtils: {
      syncCurrentUser: typeof syncCurrentUserToLookup;
      manuallyAddUser: typeof manuallyAddUserToLookup;
      batchAddUsers: typeof batchAddUsersToLookup;
      checkUser: typeof checkUserInLookup;
      getAllUsers: typeof getAllLookupUsers;
    };
  }
}

// Expose utilities globally for debugging
if (typeof window !== 'undefined') {
  window.userSyncUtils = {
    syncCurrentUser: syncCurrentUserToLookup,
    manuallyAddUser: manuallyAddUserToLookup,
    batchAddUsers: batchAddUsersToLookup,
    checkUser: checkUserInLookup,
    getAllUsers: getAllLookupUsers
  };

  console.log('🔧 User sync utilities available:');
  console.log('- userSyncUtils.syncCurrentUser() - Sync current logged-in user');
  console.log('- userSyncUtils.manuallyAddUser(email, displayName) - Add user manually');
  console.log('- userSyncUtils.checkUser(email) - Check if user exists in lookup');
  console.log('- userSyncUtils.getAllUsers() - Get all users in lookup table');
}
