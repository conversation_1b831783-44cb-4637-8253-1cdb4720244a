import React, { useState } from 'react';
import { Play, Pause, RotateCcw, Timer, Settings } from 'lucide-react';
import { useTimer } from '../hooks/useTimer';

interface WritingTimerProps {
  className?: string;
}

export const WritingTimer: React.FC<WritingTimerProps> = ({ className = '' }) => {
  const { minutes, seconds, isRunning, isFinished, start, pause, reset, setTimer } = useTimer();
  const [showSettings, setShowSettings] = useState(false);
  const [tempMinutes, setTempMinutes] = useState(25);

  const formatTime = (mins: number, secs: number) => {
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleSetTimer = () => {
    setTimer(tempMinutes);
    setShowSettings(false);
  };

  const presetTimes = [15, 25, 45, 60];

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Timer Display */}
      <div className={`flex items-center px-4 py-2 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 ${isFinished ? 'bg-green-500/20 border-green-400/40' : ''}`}>
        <Timer className="w-4 h-4 mr-2 text-white/80" />
        <span className="font-mono text-white font-medium">
          {formatTime(minutes, seconds)}
        </span>
      </div>

      {/* Controls */}
      <div className="flex items-center space-x-2">
        <button
          onClick={isRunning ? pause : start}
          className="p-2 bg-white/10 backdrop-blur-md rounded-lg border border-white/20 text-white/80 hover:text-white transition-colors"
        >
          {isRunning ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
        </button>

        <button
          onClick={reset}
          className="p-2 bg-white/10 backdrop-blur-md rounded-lg border border-white/20 text-white/80 hover:text-white transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
        </button>

        <button
          onClick={() => setShowSettings(!showSettings)}
          className="p-2 bg-white/10 backdrop-blur-md rounded-lg border border-white/20 text-white/80 hover:text-white transition-colors"
        >
          <Settings className="w-4 h-4" />
        </button>
      </div>

      {/* Settings Dropdown */}
      {showSettings && (
        <div className="absolute bottom-full mb-2 right-0 bg-black/80 backdrop-blur-md rounded-xl border border-white/20 p-4 min-w-[200px] z-10">
          <h3 className="text-white font-medium mb-3">Timer Settings</h3>
          
          {/* Preset Times */}
          <div className="grid grid-cols-2 gap-2 mb-4">
            {presetTimes.map((time) => (
              <button
                key={time}
                onClick={() => {
                  setTempMinutes(time);
                  setTimer(time);
                  setShowSettings(false);
                }}
                className="px-3 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white/80 hover:text-white transition-colors text-sm"
              >
                {time}m
              </button>
            ))}
          </div>

          {/* Custom Time */}
          <div className="space-y-2">
            <label className="block text-white/80 text-sm">Custom (minutes)</label>
            <div className="flex space-x-2">
              <input
                type="number"
                min="1"
                max="120"
                value={tempMinutes}
                onChange={(e) => setTempMinutes(parseInt(e.target.value) || 1)}
                className="flex-1 px-2 py-1 bg-white/20 border border-white/30 rounded text-white placeholder-white/60 text-sm"
              />
              <button
                onClick={handleSetTimer}
                className="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm transition-colors"
              >
                Set
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};