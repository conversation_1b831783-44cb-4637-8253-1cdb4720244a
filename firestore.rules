rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // User profiles
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Books
    match /books/{bookId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.ownerId;
      
      // Book subcollections
      match /{collection}/{docId} {
        allow read, write: if request.auth != null && get(/databases/$(database)/documents/books/$(bookId)).data.ownerId == request.auth.uid;
      }
    }
    
    // User lookup table for email-to-UID mapping
    match /userLookup/{email} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == resource.data.uid;
    }

    // Shared documents
    match /sharedDocuments/{sharedDocId} {
      // Allow sender to read and write
      allow read, write: if request.auth != null && request.auth.uid == resource.data.senderId;

      // Allow recipient to read if they are the recipient (using UID now)
      allow read: if request.auth != null && request.auth.uid == resource.data.recipientId;

      // Allow recipient to update specific fields
      allow update: if request.auth != null &&
                     request.auth.uid == resource.data.recipientId &&
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'updatedAt', 'acceptedAt', 'lastAccessedAt', 'accessCount', 'returnMessage']);

      // Shared document subcollections
      match /bookData/{docId} {
        allow read: if request.auth != null &&
                    (get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.senderId == request.auth.uid ||
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.recipientId == request.auth.uid);

        // Allow recipient to write if they have edit permission
        allow write: if request.auth != null &&
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.recipientId == request.auth.uid &&
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.permission == 'edit';

        // Always allow sender to write
        allow write: if request.auth != null &&
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.senderId == request.auth.uid;
      }

      // Comments subcollection
      match /comments/{commentId} {
        allow read: if request.auth != null &&
                    (get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.senderId == request.auth.uid ||
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.recipientId == request.auth.uid);

        allow create: if request.auth != null &&
                      (get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.senderId == request.auth.uid ||
                       get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.recipientId == request.auth.uid) &&
                      request.resource.data.userId == request.auth.uid;

        allow update: if request.auth != null &&
                      request.auth.uid == resource.data.userId &&
                      request.resource.data.diff(resource.data).affectedKeys().hasOnly(['content', 'updatedAt', 'editedAt', 'isResolved', 'resolvedAt', 'resolvedBy']);
      }
    }

    // Share invitations (tokens)
    match /shareInvitations/{invitationId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update: if request.auth != null &&
                    request.resource.data.diff(resource.data).affectedKeys().hasOnly(['usedAt']);
    }

    // Share activity logs
    match /shareActivity/{activityId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && request.resource.data.userId == request.auth.uid;
    }

    // Notifications
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null;
    }
    
    // Writing sessions and goals
    match /writingSessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    match /writingGoals/{goalId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // User settings
    match /userSettings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
