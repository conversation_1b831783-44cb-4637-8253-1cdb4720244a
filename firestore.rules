rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // User profiles
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Books
    match /books/{bookId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.ownerId;
      
      // Book subcollections
      match /{collection}/{docId} {
        allow read, write: if request.auth != null && get(/databases/$(database)/documents/books/$(bookId)).data.ownerId == request.auth.uid;
      }
    }
    


    // Notifications
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
      allow create: if request.auth != null;
    }
    
    // Writing sessions and goals
    match /writingSessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    match /writingGoals/{goalId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // User settings
    match /userSettings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
