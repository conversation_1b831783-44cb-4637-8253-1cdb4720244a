rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // User profiles
    match /users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Books
    match /books/{bookId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.ownerId;
      
      // Book subcollections
      match /{collection}/{docId} {
        allow read, write: if request.auth != null && get(/databases/$(database)/documents/books/$(bookId)).data.ownerId == request.auth.uid;
      }
    }
    
    // Shared documents
    match /sharedDocuments/{sharedDocId} {
      // Allow sender to read and write
      allow read, write: if request.auth != null && request.auth.uid == resource.data.senderId;
      
      // Allow recipient to read if they are the recipient
      allow read: if request.auth != null && request.auth.email == resource.data.recipientEmail;
      
      // Allow recipient to update status and add comments
      allow update: if request.auth != null && 
                     request.auth.email == resource.data.recipientEmail && 
                     request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'updatedAt']);
      
      // Shared document subcollections
      match /bookData/{docId} {
        allow read: if request.auth != null && 
                    (get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.senderId == request.auth.uid || 
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.recipientEmail == request.auth.email);
        
        // Allow recipient to write if they have edit permission
        allow write: if request.auth != null && 
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.recipientEmail == request.auth.email && 
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.permission == 'edit';
        
        // Always allow sender to write
        allow write: if request.auth != null && 
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.senderId == request.auth.uid;
      }
      
      // Comments subcollection
      match /comments/{commentId} {
        allow read: if request.auth != null && 
                    (get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.senderId == request.auth.uid || 
                     get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.recipientEmail == request.auth.email);
        
        allow create: if request.auth != null && 
                      (get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.senderId == request.auth.uid || 
                       get(/databases/$(database)/documents/sharedDocuments/$(sharedDocId)).data.recipientEmail == request.auth.email);
      }
    }
    
    // Writing sessions and goals
    match /writingSessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    match /writingGoals/{goalId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // User settings
    match /userSettings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
