import React, { useState, useEffect, useCallback } from 'react';
import { debounce } from '../utils/debounce';
import { X, MessageSquare, Send, Download, ArrowLeft, CheckCircle } from 'lucide-react';
import { Book, Chapter, Scene } from '../types';
import { RichTextEditor } from './RichTextEditor';
import { useDocumentSharing } from '../hooks/useDocumentSharing';

interface SharedDocumentEditorProps {
  sharedDocument: any;
  book: Book;
  userId: string;
  userName: string;
  onClose: () => void;
  onComplete: () => void;
  backgroundImage: string;
}

export const SharedDocumentEditor: React.FC<SharedDocumentEditorProps> = ({
  sharedDocument,
  book,
  userId,
  userName,
  onClose,
  onComplete,
  backgroundImage
}) => {
  const [selectedChapter, setSelectedChapter] = useState<Chapter | null>(null);
  const [selectedScene, setSelectedScene] = useState<Scene | null>(null);
  const [comments, setComments] = useState<any[]>([]);
  const [newComment, setNewComment] = useState('');
  const [showComments, setShowComments] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [returnMessage, setReturnMessage] = useState('');
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const { 
    addComment, 
    getComments, 
    updateSharedDocument, 
    completeSharedDocument,
    exportSharedDocumentToPDF 
  } = useDocumentSharing({ userId, userName });

  // Create a debounced version of updateSharedDocument
  const debouncedUpdateDocument = useCallback(
    debounce((docId: string, updatedBook: any) => {
      setIsSaving(true);
      updateSharedDocument(docId, updatedBook)
        .then(() => {
          setIsSaving(false);
        })
        .catch(err => {
          console.error('Error auto-saving document:', err);
          setIsSaving(false);
        });
    }, 2000), // 2 second delay for autosave
    [updateSharedDocument]
  );

  // Initialize with first chapter and scene
  useEffect(() => {
    try {
      if (!book || !book.chapters) {
        console.error('Invalid book data');
        return;
      }
      
      if (book.chapters.length > 0) {
        const firstChapter = book.chapters[0];
        setSelectedChapter(firstChapter);
        
        if (firstChapter.scenes && firstChapter.scenes.length > 0) {
          setSelectedScene(firstChapter.scenes[0]);
          setContent(firstChapter.scenes[0].content || '');
        }
      }
      
      loadComments();
    } catch (err) {
      console.error('Error initializing editor:', err);
    }
  }, [book]);

  const loadComments = async () => {
    setIsLoading(true);
    try {
      if (!sharedDocument || !sharedDocument.id) {
        console.error('Invalid shared document data');
        return;
      }
      
      const loadedComments = await getComments(sharedDocument.id);
      setComments(loadedComments || []);
    } catch (err) {
      console.error('Error loading comments:', err);
      // Don't show alert for this as it's not a user-initiated action
      setComments([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim() || !selectedScene) {
      if (!selectedScene) {
        alert('Please select a scene before adding a comment.');
      }
      return;
    }
    
    try {
      const result = await addComment(
        sharedDocument.id,
        newComment.trim(),
        selectedScene ? {
          chapterId: selectedChapter?.id || '',
          sceneId: selectedScene.id,
          textPosition: 0 // For simplicity, not implementing text position selection
        } : null
      );
      
      if (result.success) {
        setNewComment('');
        await loadComments();
      } else {
        alert('Failed to add comment. Please try again.');
      }
    } catch (err) {
      console.error('Error adding comment:', err);
      alert('An error occurred while adding your comment.');
    }
  };

  const handleContentChange = (newContent: string) => {
    try {
      setContent(newContent);
      
      if (!selectedScene || !selectedChapter) {
        return;
      }
      
      // Update the scene content locally
      const updatedScene = { ...selectedScene, content: newContent };
      const updatedChapter = {
        ...selectedChapter,
        scenes: selectedChapter.scenes.map(scene =>
          scene.id === selectedScene.id ? updatedScene : scene
        )
      };
      
      // Update the book with the new chapter
      const updatedBook = {
        ...book,
        chapters: book.chapters.map(chapter =>
          chapter.id === selectedChapter.id ? updatedChapter : chapter
        )
      };
      
      // Auto-save changes to Firebase using debounced function
      if (sharedDocument?.metadata?.permission === 'edit') {
        debouncedUpdateDocument(sharedDocument.id, updatedBook);
      }
    } catch (err) {
      console.error('Error handling content change:', err);
    }
  };

  const handleSelectChapter = (chapter: Chapter) => {
    setSelectedChapter(chapter);
    if (chapter.scenes.length > 0) {
      setSelectedScene(chapter.scenes[0]);
      setContent(chapter.scenes[0].content);
    } else {
      setSelectedScene(null);
      setContent('');
    }
  };

  const handleSelectScene = (scene: Scene) => {
    setSelectedScene(scene);
    setContent(scene.content);
  };

  const handleCompleteEditing = async () => {
    setIsSubmitting(true);
    try {
      const result = await completeSharedDocument(sharedDocument.id, book, returnMessage);
      if (result.success) {
        onComplete();
      } else {
        alert('Failed to complete document editing. Please try again.');
      }
    } catch (err) {
      console.error('Error completing document:', err);
      alert('An error occurred while completing document editing.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleExportToPDF = () => {
    exportSharedDocumentToPDF(book);
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div 
      className="min-h-screen relative"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="absolute inset-0 bg-black/20" />
      
      <div className="relative z-10 flex min-h-screen">
        {/* Sidebar */}
        <div className="w-80 p-6 flex flex-col">
          <div className="bg-black/60 backdrop-blur-md rounded-2xl border border-white/10 flex-1 p-6 overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-2xl font-serif font-regular text-white">Shared Document</h1>
              <button
                onClick={onClose}
                className="text-white/60 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
            </div>
            
            <div className="mb-6">
              <div className="p-3 bg-white/10 border border-white/20 rounded-xl">
                <h2 className="text-xl font-serif font-semibold text-white">{book.title}</h2>
                <p className="text-white/60 text-sm font-sans mt-1">
                  {book.projectType.charAt(0).toUpperCase() + book.projectType.slice(1).replace('-', ' ')} by {book.author}
                </p>
                <div className="mt-3 pt-3 border-t border-white/10">
                  <p className="text-white/80 text-sm font-sans">
                    {sharedDocument.metadata.permission === 'edit' 
                      ? 'You can edit this document' 
                      : 'View only'}
                  </p>
                </div>
              </div>
            </div>

            {/* Chapters */}
            <div className="space-y-2 mb-6">
              <h3 className="text-white/80 font-sans text-sm mb-3">Chapters</h3>
              
              {book.chapters.map((chapter) => (
                <div key={chapter.id}>
                  <button
                    onClick={() => handleSelectChapter(chapter)}
                    className={`w-full flex items-center justify-between p-3 rounded-lg text-left transition-colors ${
                      selectedChapter?.id === chapter.id
                        ? 'bg-white/10 text-white/90'
                        : 'text-white/80 hover:bg-white/5 hover:text-white'
                    }`}
                  >
                    <span className="font-sans">{chapter.title}</span>
                  </button>

                  {selectedChapter?.id === chapter.id && (
                    <div className="ml-4 mt-2 space-y-1">
                      {chapter.scenes.map((scene) => (
                        <button
                          key={scene.id}
                          onClick={() => handleSelectScene(scene)}
                          className={`w-full flex items-center justify-between p-2 rounded-lg text-left transition-colors ${
                            selectedScene?.id === scene.id
                              ? 'bg-white/20 text-white'
                              : 'text-white/60 hover:bg-white/10 hover:text-white/80'
                          }`}
                        >
                          <span className="font-sans text-sm">{scene.title}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <button
                onClick={handleExportToPDF}
                className="w-full flex items-center p-3 rounded-lg text-white/80 hover:bg-white/10 hover:text-white transition-colors"
              >
                <Download className="w-4 h-4 mr-3" />
                <span className="font-sans">Export to PDF</span>
              </button>
              
              {sharedDocument.metadata.permission === 'edit' && (
                <div className="p-4 bg-white/5 border border-white/10 rounded-xl">
                  <h3 className="text-white font-sans font-medium mb-2">Complete Editing</h3>
                  <textarea
                    value={returnMessage}
                    onChange={(e) => setReturnMessage(e.target.value)}
                    className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/60 font-sans mb-3 resize-none"
                    placeholder="Add a message about your changes..."
                    rows={3}
                  />
                  <button
                    onClick={handleCompleteEditing}
                    disabled={isSubmitting}
                    className="w-full flex items-center justify-center p-2 bg-white/20 hover:bg-white/30 border border-white/30 rounded-lg text-white font-sans transition-colors disabled:opacity-50"
                  >
                    {isSubmitting ? (
                      <span>Submitting...</span>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        <span>Complete & Return</span>
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Editor */}
        <div className="flex-1 p-6 flex flex-col min-h-0 h-screen">
          {/* Header */}
          <div className="bg-black/40 backdrop-blur-md rounded-xl border border-white/20 p-3 mb-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-lg font-serif font-regular text-white">
                  {selectedChapter?.title} {selectedScene && `- ${selectedScene.title}`}
                </h1>
                {sharedDocument.metadata.permission === 'edit' && (
                  <div className="text-xs text-white/60 mt-1">
                    {isSaving ? 'Saving...' : 'Changes auto-saved'}
                  </div>
                )}
              </div>
              <button
                onClick={() => setShowComments(!showComments)}
                className={`flex items-center px-3 py-1 rounded-lg border transition-colors ${
                  showComments
                    ? 'bg-white/20 border-white/30 text-white'
                    : 'bg-transparent border-white/10 text-white/70'
                }`}
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                <span className="font-sans text-sm">Comments ({comments.length})</span>
              </button>
            </div>
          </div>

          {/* Editor and Comments */}
          <div className="flex-1 mb-4 min-h-0 flex">
            {/* Editor */}
            <div className={`${showComments ? 'w-2/3' : 'w-full'} transition-all duration-300`}>
              {selectedScene ? (
                <RichTextEditor
                  content={content}
                  onChange={handleContentChange}
                  placeholder="This document is empty..."
                  className="h-full"
                  sceneId={selectedScene.id}
                  readOnly={sharedDocument.metadata.permission !== 'edit'}
                />
              ) : (
                <div className="bg-white rounded-2xl shadow-2xl h-full flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <h3 className="text-xl font-serif mb-2">Select a scene to start</h3>
                    <p className="font-sans">Choose a scene from the sidebar to view or edit.</p>
                  </div>
                </div>
              )}
            </div>

            {/* Comments Panel */}
            {showComments && (
              <div className="w-1/3 ml-4 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-4 flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-serif text-white">Comments</h3>
                  <button
                    onClick={() => setShowComments(false)}
                    className="text-white/60 hover:text-white"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                {/* Comments List */}
                <div className="flex-1 overflow-y-auto mb-4">
                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-white"></div>
                    </div>
                  ) : comments.length > 0 ? (
                    <div className="space-y-4">
                      {comments.map((comment) => (
                        <div 
                          key={comment.id} 
                          className="p-3 bg-white/10 border border-white/20 rounded-lg"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-white/90 font-sans font-medium">{comment.userName}</span>
                            <span className="text-white/60 text-xs">{formatDate(comment.createdAt)}</span>
                          </div>
                          <p className="text-white/80 font-sans">{comment.content}</p>
                          {comment.position && (
                            <div className="mt-2 text-xs text-white/60 font-sans">
                              {book.chapters.find(c => c.id === comment.position.chapterId)?.title} - 
                              {book.chapters
                                .find(c => c.id === comment.position.chapterId)
                                ?.scenes.find(s => s.id === comment.position.sceneId)?.title}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-white/60 font-sans">No comments yet</p>
                    </div>
                  )}
                </div>

                {/* Add Comment */}
                <div className="border-t border-white/20 pt-4">
                  <div className="flex">
                    <input
                      type="text"
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      className="flex-1 px-3 py-2 bg-white/20 border border-white/30 rounded-l-lg text-white placeholder-white/60 font-sans focus:outline-none"
                      placeholder="Add a comment..."
                      onKeyPress={(e) => e.key === 'Enter' && handleAddComment()}
                    />
                    <button
                      onClick={handleAddComment}
                      className="px-3 py-2 bg-white/30 border border-white/40 rounded-r-lg text-white hover:bg-white/40 transition-colors"
                    >
                      <Send className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};