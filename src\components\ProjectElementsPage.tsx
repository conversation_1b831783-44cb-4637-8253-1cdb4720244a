import React, { useState } from 'react';
import { ArrowLeft, Users, Target, Globe, Plus, Edit, Trash2, Download } from 'lucide-react';
import { Book, Character, PlotPoint } from '../types';
import { createNew<PERSON>haracter, createNewPlotPoint } from '../utils/bookUtils';
import { useImageBrightness } from '../hooks/useImageBrightness';
import { exportBookToPDF } from '../utils/pdfExport';
import { WorldbuildingTab } from './WorldbuildingTab';

interface ProjectElementsPageProps {
  book: Book;
  onUpdateBook: (book: Book) => void;
  onBack: () => void;
  backgroundImage: string;
}

export const ProjectElementsPage: React.FC<ProjectElementsPageProps> = ({
  book,
  onUpdateBook,
  onBack,
  backgroundImage
}) => {
  const [activeTab, setActiveTab] = useState<'characters' | 'plot' | 'worldbuilding'>('characters');
  const [showNewCharacterForm, setShowNewCharacterForm] = useState(false);
  const [showNewPlotForm, setShowNewPlotForm] = useState(false);
  const [editingCharacter, setEditingCharacter] = useState<string | null>(null);
  const [editingPlot, setEditingPlot] = useState<string | null>(null);
  
  // Form states
  const [newCharacterName, setNewCharacterName] = useState('');
  const [newPlotTitle, setNewPlotTitle] = useState('');
  const [characterForm, setCharacterForm] = useState({
    name: '',
    description: '',
    role: '',
    appearance: '',
    personality: '',
    backstory: '',
    goals: '',
    notes: ''
  });
  const [plotForm, setPlotForm] = useState({
    title: '',
    description: ''
  });
  const isBackgroundDark = useImageBrightness(backgroundImage);
  const glassClass = 'bg-black/60';
  const glassClassLight = 'bg-black/40';

  const handleCreateCharacter = () => {
    if (!newCharacterName.trim()) return;
    
    const newCharacter = createNewCharacter(newCharacterName.trim());
    const updatedBook = {
      ...book,
      characters: [...book.characters, newCharacter],
      updatedAt: new Date().toISOString()
    };
    
    onUpdateBook(updatedBook);
    setNewCharacterName('');
    setShowNewCharacterForm(false);
    
    // Auto-open editing view
    startEditingCharacter(newCharacter);
  };

  const handleCreatePlot = () => {
    if (!newPlotTitle.trim()) return;
    
    const newPlot = createNewPlotPoint(newPlotTitle.trim(), book.plotPoints.length);
    const updatedBook = {
      ...book,
      plotPoints: [...book.plotPoints, newPlot],
      updatedAt: new Date().toISOString()
    };
    
    onUpdateBook(updatedBook);
    setNewPlotTitle('');
    setShowNewPlotForm(false);
    
    // Auto-open editing view
    startEditingPlot(newPlot);
  };

  const handleUpdateCharacter = (characterId: string, updates: Partial<Character>) => {
    const updatedBook = {
      ...book,
      characters: book.characters.map(char => 
        char.id === characterId 
          ? { ...char, ...updates, updatedAt: new Date().toISOString() }
          : char
      ),
      updatedAt: new Date().toISOString()
    };
    
    onUpdateBook(updatedBook);
  };

  const handleUpdatePlot = (plotId: string, updates: Partial<PlotPoint>) => {
    const updatedBook = {
      ...book,
      plotPoints: book.plotPoints.map(plot => 
        plot.id === plotId 
          ? { ...plot, ...updates, updatedAt: new Date().toISOString() }
          : plot
      ),
      updatedAt: new Date().toISOString()
    };
    
    onUpdateBook(updatedBook);
  };

  const handleDeleteCharacter = (characterId: string) => {
    if (window.confirm('Are you sure you want to delete this character?')) {
      const updatedBook = {
        ...book,
        characters: book.characters.filter(char => char.id !== characterId),
        updatedAt: new Date().toISOString()
      };
      onUpdateBook(updatedBook);
    }
  };

  const handleDeletePlot = (plotId: string) => {
    if (window.confirm('Are you sure you want to delete this plot point?')) {
      const updatedBook = {
        ...book,
        plotPoints: book.plotPoints.filter(plot => plot.id !== plotId),
        updatedAt: new Date().toISOString()
      };
      onUpdateBook(updatedBook);
    }
  };

  const handleExportToPDF = () => {
    exportBookToPDF(book);
  };

  const startEditingCharacter = (character: Character) => {
    setEditingCharacter(character.id);
    setCharacterForm({
      name: character.name,
      description: character.description || '',
      role: character.role || '',
      appearance: character.appearance || '',
      personality: character.personality || '',
      backstory: character.backstory || '',
      goals: character.goals || '',
      notes: character.notes || ''
    });
  };

  const saveCharacterEdit = () => {
    if (!editingCharacter) return;
    handleUpdateCharacter(editingCharacter, characterForm);
    setEditingCharacter(null);
    setCharacterForm({
      name: '',
      description: '',
      role: '',
      appearance: '',
      personality: '',
      backstory: '',
      goals: '',
      notes: ''
    });
  };

  const startEditingPlot = (plot: PlotPoint) => {
    setEditingPlot(plot.id);
    setPlotForm({
      title: plot.title,
      description: plot.description || ''
    });
  };

  const savePlotEdit = () => {
    if (!editingPlot) return;
    handleUpdatePlot(editingPlot, plotForm);
    setEditingPlot(null);
    setPlotForm({ title: '', description: '' });
  };

  return (
    <div 
      className="min-h-screen relative"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="absolute inset-0 bg-black/20" />
      
      <div className="relative z-10 flex h-screen">
        {/* Sidebar */}
        <div className="w-80 p-6 flex flex-col">
          <div className={`${glassClass} backdrop-blur-md rounded-2xl border border-white/10 flex-1 p-6 overflow-y-auto`}>
            <div className="mb-6">
              <button
                onClick={onBack}
                className="flex items-center text-white/80 hover:text-white transition-colors mb-4"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                <span className="font-sans">Back to Writing</span>
              </button>
              <h1 className="text-2xl font-serif font-semibold text-white mb-2">Project Elements</h1>
              <div className="text-sm text-white/60 font-sans">
                {book.title} by {book.author}
              </div>
            </div>
            
            {/* Navigation */}
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('characters')}
                className={`w-full flex items-center p-3 rounded-lg text-left transition-colors ${
                  activeTab === 'characters'
                    ? 'bg-white/10 text-white/90'
                    : 'text-white/80 hover:bg-white/5 hover:text-white'
                }`}
              >
                <Users className="w-4 h-4 mr-3" />
                <span className="font-sans">Characters</span>
              </button>
              <button
                onClick={() => setActiveTab('plot')}
                className={`w-full flex items-center p-3 rounded-lg text-left transition-colors ${
                  activeTab === 'plot'
                    ? 'bg-white/10 text-white/90'
                    : 'text-white/80 hover:bg-white/5 hover:text-white'
                }`}
              >
                <Target className="w-4 h-4 mr-3" />
                <span className="font-sans">Plot Points</span>
              </button>
              <button
                onClick={() => setActiveTab('worldbuilding')}
                className={`w-full flex items-center p-3 rounded-lg text-left transition-colors ${
                  activeTab === 'worldbuilding'
                    ? 'bg-white/10 text-white/90'
                    : 'text-white/80 hover:bg-white/5 hover:text-white'
                }`}
              >
                <Globe className="w-4 h-4 mr-3" />
                <span className="font-sans">Worldbuilding</span>
              </button>
              
              {/* Export Section */}
              <div className="pt-4 border-t border-white/20">
                <button
                  onClick={handleExportToPDF}
                  className="w-full flex items-center p-3 rounded-lg text-left transition-colors text-white/80 hover:bg-white/5 hover:text-white"
                >
                  <Download className="w-4 h-4 mr-3" />
                  <span className="font-sans">Export to PDF</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6 overflow-y-auto">
        {activeTab === 'characters' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-serif font-semibold text-white">Characters</h2>
              <button
                onClick={() => setShowNewCharacterForm(true)}
                className="flex items-center px-4 py-2 bg-black/30 backdrop-blur-md hover:bg-black/40 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Character
              </button>
            </div>

            {showNewCharacterForm && (
              <div className={`p-6 ${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 shadow-sm`}>
                <input
                  type="text"
                  value={newCharacterName}
                  onChange={(e) => setNewCharacterName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleCreateCharacter()}
                  placeholder="Character name..."
                  className="w-full px-4 py-3 bg-black/30 border border-white/20 rounded-lg font-sans text-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                  autoFocus
                />
                <div className="flex space-x-3 mt-4">
                  <button
                    onClick={handleCreateCharacter}
                    className="px-6 py-2 bg-black/30 backdrop-blur-md hover:bg-black/40 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
                  >
                    Add Character
                  </button>
                  <button
                    onClick={() => setShowNewCharacterForm(false)}
                    className="px-6 py-2 bg-black/20 hover:bg-black/30 text-white/80 font-sans font-medium rounded-lg border border-white/10 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-6">
              {book.characters.map((character) => (
                <div key={character.id} className={`p-6 ${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 shadow-sm`}>
                  {editingCharacter === character.id ? (
                    <div className="space-y-4 w-full">
                      <input
                        type="text"
                        value={characterForm.name}
                        onChange={(e) => setCharacterForm({ ...characterForm, name: e.target.value })}
                        className="w-full px-4 py-3 bg-black/30 border border-white/20 rounded-lg font-sans font-semibold text-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                      />
                      <div className="grid grid-cols-2 gap-4">
                        <input
                          type="text"
                          value={characterForm.role}
                          onChange={(e) => setCharacterForm({ ...characterForm, role: e.target.value })}
                          placeholder="Role (e.g., protagonist, villain)"
                          className="px-3 py-2 bg-black/30 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                        />
                        <input
                          type="text"
                          value={characterForm.appearance}
                          onChange={(e) => setCharacterForm({ ...characterForm, appearance: e.target.value })}
                          placeholder="Appearance"
                          className="px-3 py-2 bg-black/30 border border-white/20 rounded-lg font-sans text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                        />
                      </div>
                      <textarea
                        value={characterForm.description}
                        onChange={(e) => setCharacterForm({ ...characterForm, description: e.target.value })}
                        placeholder="Description"
                        className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg font-sans resize-none text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                        rows={3}
                      />
                      <textarea
                        value={characterForm.personality}
                        onChange={(e) => setCharacterForm({ ...characterForm, personality: e.target.value })}
                        placeholder="Personality"
                        className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg font-sans resize-none text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                        rows={3}
                      />
                      <textarea
                        value={characterForm.backstory}
                        onChange={(e) => setCharacterForm({ ...characterForm, backstory: e.target.value })}
                        placeholder="Backstory"
                        className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg font-sans resize-none text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                        rows={3}
                      />
                      <textarea
                        value={characterForm.goals}
                        onChange={(e) => setCharacterForm({ ...characterForm, goals: e.target.value })}
                        placeholder="Goals & Motivations"
                        className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg font-sans resize-none text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                        rows={3}
                      />
                      <textarea
                        value={characterForm.notes}
                        onChange={(e) => setCharacterForm({ ...characterForm, notes: e.target.value })}
                        placeholder="Additional notes"
                        className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg font-sans resize-none text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                        rows={3}
                      />
                      <div className="flex space-x-3">
                        <button
                          onClick={saveCharacterEdit}
                          className="px-4 py-2 bg-black/30 backdrop-blur-md hover:bg-black/40 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
                        >
                          Save Changes
                        </button>
                        <button
                          onClick={() => setEditingCharacter(null)}
                          className="px-4 py-2 bg-black/20 hover:bg-black/30 text-white/80 font-sans font-medium rounded-lg border border-white/10 transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-xl font-serif font-semibold text-white">{character.name}</h3>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => startEditingCharacter(character)}
                            className="text-white/60 hover:text-white"
                          >
                            <Edit className="w-5 h-5" />
                          </button>
                          <button
                            onClick={() => handleDeleteCharacter(character.id)}
                            className="text-white/60 hover:text-red-400"
                          >
                            <Trash2 className="w-5 h-5" />
                          </button>
                        </div>
                      </div>
                      {character.role && (
                        <p className="font-sans text-blue-300 mb-3 font-medium">{character.role}</p>
                      )}
                      {character.description && (
                        <p className="font-sans text-white/80 mb-3">{character.description}</p>
                      )}
                      {character.appearance && (
                        <div className="mb-3">
                          <h4 className="font-sans font-medium text-white mb-1">Appearance</h4>
                          <p className="font-sans text-white/70 text-sm">{character.appearance}</p>
                        </div>
                      )}
                      {character.personality && (
                        <div className="mb-3">
                          <h4 className="font-sans font-medium text-white mb-1">Personality</h4>
                          <p className="font-sans text-white/70 text-sm">{character.personality}</p>
                        </div>
                      )}
                      {character.backstory && (
                        <div className="mb-3">
                          <h4 className="font-sans font-medium text-white mb-1">Backstory</h4>
                          <p className="font-sans text-white/70 text-sm">{character.backstory}</p>
                        </div>
                      )}
                      {character.goals && (
                        <div className="mb-3">
                          <h4 className="font-sans font-medium text-white mb-1">Goals & Motivations</h4>
                          <p className="font-sans text-white/70 text-sm">{character.goals}</p>
                        </div>
                      )}
                      {character.notes && (
                        <div>
                          <h4 className="font-sans font-medium text-white mb-1">Notes</h4>
                          <p className="font-sans text-white/70 text-sm">{character.notes}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'plot' && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-serif font-semibold text-white">Plot Points</h2>
              <button
                onClick={() => setShowNewPlotForm(true)}
                className="flex items-center px-4 py-2 bg-black/30 backdrop-blur-md hover:bg-black/40 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Plot Point
              </button>
            </div>

            {showNewPlotForm && (
              <div className={`p-6 ${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 shadow-sm`}>
                <input
                  type="text"
                  value={newPlotTitle}
                  onChange={(e) => setNewPlotTitle(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleCreatePlot()}
                  placeholder="Plot point title..."
                  className="w-full px-4 py-3 bg-black/30 border border-white/20 rounded-lg font-sans text-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                  autoFocus
                />
                <div className="flex space-x-3 mt-4">
                  <button
                    onClick={handleCreatePlot}
                    className="px-6 py-2 bg-black/30 backdrop-blur-md hover:bg-black/40 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
                  >
                    Add Plot Point
                  </button>
                  <button
                    onClick={() => setShowNewPlotForm(false)}
                    className="px-6 py-2 bg-black/20 hover:bg-black/30 text-white/80 font-sans font-medium rounded-lg border border-white/10 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              {book.plotPoints.map((plot) => (
                <div key={plot.id} className={`p-6 ${glassClassLight} backdrop-blur-md rounded-xl border border-white/20 shadow-sm`}>
                  {editingPlot === plot.id ? (
                    <div className="space-y-4 w-full">
                      <input
                        type="text"
                        value={plotForm.title}
                        onChange={(e) => setPlotForm({ ...plotForm, title: e.target.value })}
                        className="w-full px-4 py-3 bg-black/30 border border-white/20 rounded-lg font-sans font-semibold text-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                      />
                      <textarea
                        value={plotForm.description}
                        onChange={(e) => setPlotForm({ ...plotForm, description: e.target.value })}
                        placeholder="Plot point description..."
                        className="w-full px-3 py-2 bg-black/30 border border-white/20 rounded-lg font-sans resize-none text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                        rows={4}
                      />
                      <div className="flex space-x-3">
                        <button
                          onClick={savePlotEdit}
                          className="px-4 py-2 bg-black/30 backdrop-blur-md hover:bg-black/40 text-white font-sans font-medium rounded-lg border border-white/20 transition-colors"
                        >
                          Save Changes
                        </button>
                        <button
                          onClick={() => setEditingPlot(null)}
                          className="px-4 py-2 bg-black/20 hover:bg-black/30 text-white/80 font-sans font-medium rounded-lg border border-white/10 transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={plot.completed}
                            onChange={(e) => handleUpdatePlot(plot.id, { completed: e.target.checked })}
                            className="mr-4 w-5 h-5 text-blue-400 bg-white/20 border-white/30 rounded focus:ring-blue-400 focus:ring-2"
                          />
                          <h3 className={`text-xl font-serif font-semibold ${plot.completed ? 'text-white/50 line-through' : 'text-white'}`}>
                            {plot.title}
                          </h3>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => startEditingPlot(plot)}
                            className="text-white/60 hover:text-white"
                          >
                            <Edit className="w-5 h-5" />
                          </button>
                          <button
                            onClick={() => handleDeletePlot(plot.id)}
                            className="text-white/60 hover:text-red-400"
                          >
                            <Trash2 className="w-5 h-5" />
                          </button>
                        </div>
                      </div>
                      {plot.description && (
                        <p className="font-sans text-white/80 ml-9">{plot.description}</p>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'worldbuilding' && (
          <WorldbuildingTab
            book={book}
            onUpdateBook={onUpdateBook}
            glassClass={glassClass}
            glassClassLight={glassClassLight}
          />
        )}

        </div>
      </div>
    </div>
  );
};