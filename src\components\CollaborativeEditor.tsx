import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Check, X, User, Clock } from 'lucide-react';
import type { EditChange, UserRole } from '../types';
import { CollaborativeEditingService } from '../services/collaborativeEditingService';

interface CollaborativeEditorProps {
  content: string;
  onChange: (content: string) => void;
  bookId: string;
  sceneId: string;
  currentUserId: string;
  currentUserName: string;
  userRole: UserRole;
  editChanges?: EditChange[];
  onAcceptChange?: (changeId: string) => void;
  onRejectChange?: (changeId: string) => void;
  className?: string;
  placeholder?: string;
}

interface PendingChange {
  id: string;
  type: 'insert' | 'delete' | 'replace';
  position: number;
  originalText: string;
  newText: string;
  userId: string;
  userName: string;
}

export const CollaborativeEditor: React.FC<CollaborativeEditorProps> = ({
  content,
  onChange,
  bookId,
  sceneId,
  currentUserId,
  currentUserName,
  userRole,
  editChanges = [],
  onAcceptChange,
  onRejectChange,
  className = '',
  placeholder = 'Start writing...'
}) => {
  const [localContent, setLocalContent] = useState(content);
  const [pendingChanges, setPendingChanges] = useState<PendingChange[]>([]);
  const [showChangeReview, setShowChangeReview] = useState(false);
  const editorRef = useRef<HTMLDivElement>(null);
  const lastContentRef = useRef(content);

  // Update local content when prop changes
  useEffect(() => {
    if (content !== lastContentRef.current) {
      setLocalContent(content);
      lastContentRef.current = content;
    }
  }, [content]);

  // Render content with tracked changes highlighted
  const renderContentWithChanges = useCallback(() => {
    let renderedContent = localContent;
    const pendingEditChanges = editChanges.filter(change => change.isAccepted === null);

    // Apply visual styling for pending changes
    pendingEditChanges.forEach(change => {
      if (change.type === 'insert' || change.type === 'replace') {
        const beforeText = renderedContent.substring(0, change.position);
        const afterText = renderedContent.substring(change.position + change.originalText.length);
        
        const changeElement = `<span class="edit-change edit-${change.type}" data-change-id="${change.id}" data-user="${change.userName}">
          ${change.newText}
          <span class="change-controls">
            <button class="accept-btn" onclick="window.acceptChange('${change.id}')">✓</button>
            <button class="reject-btn" onclick="window.rejectChange('${change.id}')">✗</button>
            <span class="change-author">${change.userName}</span>
          </span>
        </span>`;
        
        renderedContent = beforeText + changeElement + afterText;
      }
    });

    return renderedContent;
  }, [localContent, editChanges]);

  // Handle content changes
  const handleContentChange = useCallback(async (newContent: string) => {
    setLocalContent(newContent);
    
    // If user is an editor (not author), track the change
    if (userRole === 'editor') {
      const change = detectChange(lastContentRef.current, newContent);
      if (change) {
        try {
          await CollaborativeEditingService.trackEditChange(bookId, sceneId, {
            userId: currentUserId,
            userName: currentUserName,
            type: change.type,
            position: change.position,
            originalText: change.originalText,
            newText: change.newText,
            isAccepted: null
          });
        } catch (error) {
          console.error('Error tracking change:', error);
        }
      }
    }
    
    // Always call onChange for real-time sync
    onChange(newContent);
    lastContentRef.current = newContent;
  }, [bookId, sceneId, currentUserId, currentUserName, userRole, onChange]);

  // Simple change detection (in a real implementation, you'd use a more sophisticated diff algorithm)
  const detectChange = (oldText: string, newText: string): Omit<EditChange, 'id' | 'userId' | 'userName' | 'timestamp' | 'isAccepted'> | null => {
    if (oldText === newText) return null;

    // Simple implementation - detect if text was added or removed
    if (newText.length > oldText.length) {
      // Text was inserted
      const insertPosition = findInsertPosition(oldText, newText);
      return {
        type: 'insert',
        position: insertPosition,
        originalText: '',
        newText: newText.substring(insertPosition, insertPosition + (newText.length - oldText.length))
      };
    } else if (newText.length < oldText.length) {
      // Text was deleted
      const deletePosition = findDeletePosition(oldText, newText);
      return {
        type: 'delete',
        position: deletePosition,
        originalText: oldText.substring(deletePosition, deletePosition + (oldText.length - newText.length)),
        newText: ''
      };
    } else {
      // Text was replaced (same length)
      const replacePosition = findReplacePosition(oldText, newText);
      return {
        type: 'replace',
        position: replacePosition,
        originalText: oldText.substring(replacePosition, replacePosition + 1),
        newText: newText.substring(replacePosition, replacePosition + 1)
      };
    }
  };

  // Helper functions for change detection
  const findInsertPosition = (oldText: string, newText: string): number => {
    for (let i = 0; i < Math.min(oldText.length, newText.length); i++) {
      if (oldText[i] !== newText[i]) {
        return i;
      }
    }
    return oldText.length;
  };

  const findDeletePosition = (oldText: string, newText: string): number => {
    for (let i = 0; i < Math.min(oldText.length, newText.length); i++) {
      if (oldText[i] !== newText[i]) {
        return i;
      }
    }
    return newText.length;
  };

  const findReplacePosition = (oldText: string, newText: string): number => {
    for (let i = 0; i < oldText.length; i++) {
      if (oldText[i] !== newText[i]) {
        return i;
      }
    }
    return 0;
  };

  // Handle accepting/rejecting changes
  const handleAcceptChange = useCallback(async (changeId: string) => {
    if (onAcceptChange) {
      onAcceptChange(changeId);
    }
    
    try {
      await CollaborativeEditingService.reviewEditChange(
        bookId,
        sceneId,
        changeId,
        true,
        currentUserId
      );
    } catch (error) {
      console.error('Error accepting change:', error);
    }
  }, [bookId, sceneId, currentUserId, onAcceptChange]);

  const handleRejectChange = useCallback(async (changeId: string) => {
    if (onRejectChange) {
      onRejectChange(changeId);
    }
    
    try {
      await CollaborativeEditingService.reviewEditChange(
        bookId,
        sceneId,
        changeId,
        false,
        currentUserId
      );
    } catch (error) {
      console.error('Error rejecting change:', error);
    }
  }, [bookId, sceneId, currentUserId, onRejectChange]);

  // Make accept/reject functions available globally for the inline buttons
  useEffect(() => {
    (window as any).acceptChange = handleAcceptChange;
    (window as any).rejectChange = handleRejectChange;
    
    return () => {
      delete (window as any).acceptChange;
      delete (window as any).rejectChange;
    };
  }, [handleAcceptChange, handleRejectChange]);

  const pendingEditChanges = editChanges.filter(change => change.isAccepted === null);

  return (
    <div className={`collaborative-editor ${className}`}>
      {/* Change Review Panel */}
      {userRole === 'author' && pendingEditChanges.length > 0 && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-blue-800">
              {pendingEditChanges.length} pending change{pendingEditChanges.length !== 1 ? 's' : ''}
            </span>
            <button
              onClick={() => setShowChangeReview(!showChangeReview)}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              {showChangeReview ? 'Hide' : 'Review'}
            </button>
          </div>
          
          {showChangeReview && (
            <div className="space-y-2">
              {pendingEditChanges.map(change => (
                <div key={change.id} className="flex items-center justify-between p-2 bg-white rounded border">
                  <div className="flex-1">
                    <div className="flex items-center text-sm">
                      <User className="w-3 h-3 mr-1 text-gray-500" />
                      <span className="font-medium">{change.userName}</span>
                      <Clock className="w-3 h-3 ml-2 mr-1 text-gray-500" />
                      <span className="text-gray-500">
                        {new Date(change.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-sm text-gray-700 mt-1">
                      {change.type === 'insert' && `Added: "${change.newText}"`}
                      {change.type === 'delete' && `Deleted: "${change.originalText}"`}
                      {change.type === 'replace' && `Changed "${change.originalText}" to "${change.newText}"`}
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <button
                      onClick={() => handleAcceptChange(change.id)}
                      className="p-1 text-green-600 hover:bg-green-100 rounded"
                      title="Accept change"
                    >
                      <Check className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleRejectChange(change.id)}
                      className="p-1 text-red-600 hover:bg-red-100 rounded"
                      title="Reject change"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        className="w-full h-full p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-96"
        style={{ whiteSpace: 'pre-wrap' }}
        onInput={(e) => {
          const target = e.target as HTMLDivElement;
          handleContentChange(target.textContent || '');
        }}
        dangerouslySetInnerHTML={{ 
          __html: userRole === 'author' ? renderContentWithChanges() : localContent 
        }}
        placeholder={placeholder}
      />

      {/* Editor Role Indicator */}
      <div className="mt-2 text-sm text-gray-500">
        Editing as: <span className="font-medium capitalize">{userRole}</span>
        {userRole === 'editor' && (
          <span className="ml-2 text-blue-600">• Changes will be highlighted for review</span>
        )}
      </div>

      {/* CSS for change highlighting */}
      <style jsx>{`
        .edit-change {
          background-color: #dbeafe;
          border-bottom: 2px solid #3b82f6;
          position: relative;
          padding: 2px 4px;
          margin: 0 2px;
        }
        
        .edit-change.edit-insert {
          background-color: #dcfce7;
          border-bottom-color: #22c55e;
        }
        
        .edit-change.edit-delete {
          background-color: #fee2e2;
          border-bottom-color: #ef4444;
          text-decoration: line-through;
        }
        
        .edit-change.edit-replace {
          background-color: #fef3c7;
          border-bottom-color: #f59e0b;
        }
        
        .change-controls {
          position: absolute;
          top: -30px;
          left: 0;
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          padding: 2px;
          display: none;
          z-index: 10;
        }
        
        .edit-change:hover .change-controls {
          display: flex;
          align-items: center;
          gap: 4px;
        }
        
        .accept-btn, .reject-btn {
          padding: 2px 4px;
          border: none;
          border-radius: 2px;
          cursor: pointer;
          font-size: 12px;
        }
        
        .accept-btn {
          background: #22c55e;
          color: white;
        }
        
        .reject-btn {
          background: #ef4444;
          color: white;
        }
        
        .change-author {
          font-size: 10px;
          color: #6b7280;
          margin-left: 4px;
        }
      `}</style>
    </div>
  );
};
