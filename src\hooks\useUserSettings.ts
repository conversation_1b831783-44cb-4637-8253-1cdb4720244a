import { useState, useEffect, useCallback } from 'react';
import { collection, doc, getDocs, setDoc, addDoc, query, where, serverTimestamp } from 'firebase/firestore';
import { db } from '../lib/firebase';
import { COLLECTIONS } from '../types/firebase';

interface UserSettings {
  backgroundImage: string;
}

export const useUserSettings = (userId: string) => {
  const [settings, setSettings] = useState<UserSettings>({
    backgroundImage: 'https://www.outdoorpainter.com/wp-content/uploads/2019/03/painting-clouds-Kim-Casebeer-Sunset-at-The-Clark-8x10-1024x821.jpg'
  });
  const [loading, setLoading] = useState(true);

  const loadSettings = useCallback(async () => {
    if (!userId) return;
    
    try {
      setLoading(true);
      const settingsQuery = query(
        collection(db, COLLECTIONS.USER_SETTINGS),
        where('userId', '==', userId)
      );
      const settingsSnapshot = await getDocs(settingsQuery);
      
      if (!settingsSnapshot.empty) {
        const settingsData = settingsSnapshot.docs[0].data();
        setSettings({
          backgroundImage: settingsData.backgroundImage || settings.backgroundImage
        });
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    } finally {
      setLoading(false);
    }
  }, [userId, settings.backgroundImage]);

  const updateBackgroundImage = async (backgroundImage: string) => {
    try {
      const settingsQuery = query(
        collection(db, COLLECTIONS.USER_SETTINGS),
        where('userId', '==', userId)
      );
      const settingsSnapshot = await getDocs(settingsQuery);
      
      if (settingsSnapshot.empty) {
        await addDoc(collection(db, COLLECTIONS.USER_SETTINGS), {
          userId,
          backgroundImage,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });
      } else {
        const docRef = doc(db, COLLECTIONS.USER_SETTINGS, settingsSnapshot.docs[0].id);
        await setDoc(docRef, {
          backgroundImage,
          updatedAt: serverTimestamp()
        }, { merge: true });
      }
      
      setSettings(prev => ({ ...prev, backgroundImage }));
    } catch (error) {
      console.error('Error updating background image:', error);
    }
  };

  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    loading,
    updateBackgroundImage
  };
};